<?php
/**
 * Desc 迭代流程节点 - 逻辑
 * User Long
 * Date 2024/11/9
 */

namespace app\project\logic;

use app\iterate\logic\FlowProcessAutoLogic;
use app\iterate\logic\FlowStatusTextLogic;
use app\project\model\IterationLogModel;
use app\project\model\IterationModel;
use app\project\model\IterationProcessNodeModel;
use app\project\model\TagLibraryModel;
use app\work_items\logic\TestPlanLogic;
use app\work_items\logic\WorkItemsLogic;
use app\work_items\model\WorkItemsModel;
use basic\BaseLogic;
use basic\BaseModel;
use exception\BusinessException;
use exception\NotFoundException;
use think\facade\Db;

class IterationProcessNodeLogic extends BaseLogic
{
    /**
     * 格式化nodeList供响应
     * @param $nodeList
     * @param $prevRelationData
     * @param $nextRelationData
     * @param $rowIds
     * @return array
     * User Long
     * Date 2024/11/12
     */
    private static function formatNodeList($nodeList, $prevRelationData, $nextRelationData)
    {
        $result = [];

        foreach ($nodeList as $v) {
            // 遍历前置关系数据
            $node = [];
            $node['iteration_process_node_id'] = $v['iteration_process_node_id'];
            $node['node_name'] = $v['node_name'];
            $node['status'] = $v['status'];
            $node['estimate_start_time'] = $v['estimate_start_time'];
            $node['estimate_end_time'] = $v['estimate_end_time'];
            $node['start_time'] = $v['start_time'];
            $node['end_time'] = $v['end_time'];
            $node['position'] = $v['node_data']['position'] ?? [];
            $node['row_id'] = $v['row_id'];
            $node['tip'] = $v['tip'];

            // 初始化前置节点数组
            $node['prev_node'] = [];
            foreach ($prevRelationData as $prevNode) {
                // 如果当前节点的迭代过程节点ID等于前置节点的下一个节点ID
                if ($prevNode['next_node_id'] == $v['iteration_process_node_id']){
                    // 查找对应的前置节点ID, 将前置节点ID添加到当前节点的前置节点数组中
                    $node['prev_node'][] = $nodeList->where('iteration_process_node_id', $prevNode['process_node_id'])->first()->row_id ?? 0;
                }
            }

            // 初始化后置节点数组
            $node['next_node'] = [];
            foreach ($nextRelationData as $nextNode) {
                // 如果当前节点的迭代过程节点ID等于后置节点的过程节点ID
                if ($nextNode['process_node_id'] == $v['iteration_process_node_id']){
                    // 查找对应的后置节点ID, 将后置节点ID添加到当前节点的后置节点数组中
                    $node['next_node'][] = $nodeList->where('iteration_process_node_id', $nextNode['next_node_id'])->first()->row_id ?? 0;
                }
            }
            $result[] = $node;
        }

        return $result;
    }

    /**
     * 整理自动化数据
     * @param $iterationExtends
     * @param $iterationId
     * @param $iterationProcessNodeId
     * @return mixed
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * User Long
     * Date 2024/12/14
     */
    private static function upkeepAutoData($iterationExtends, $iterationId, $iterationProcessNodeId): mixed
    {
        $iterationExtends['present_node'] = IterationProcessNodeModel::where([
            'iteration_process_node_id' => $iterationProcessNodeId
        ])->value('process_node_id');                                                                          // 当前节点

        // 进度数据
        $iterationExtends['iteration_progress'] = (new WorkItemsLogic())->completeProgress($iterationId) ?? 0; // 迭代进度
        $testPlan = TestPlanLogic::getInstance()->getAvgCompletenessByIterationId($iterationId);
        $iterationExtends['test_plan_completion_progress'] = $testPlan['test_plan']['avg_execution_progress'] ?? 0;               // 测试计划完成进度
        $iterationExtends['self_test_plan_completion_progress'] = $testPlan['dev_self_test_plan']['avg_execution_progress'] ?? 0; // 开发自测计划完成进度
        $iterationExtends['auto_completion_progress'] = $testPlan['automate']['avg_execution_progress'] ?? 0;                     // 自动化完成进度

        // 获取当前迭代工作项数据
        $workItemsData = WorkItemsLogic::findIterationDataByIterationId($iterationId);

        // 需求、缺陷、任务数量查询
        $demandWorkItemsData = $workItemsData->where('cnt_type', WorkItemsModel::CNT_TYPE_DEMAND);
        $iterationExtends['demand_number'] = $demandWorkItemsData->count();                                   // 需求数
        $iterationExtends['demand_incomplete_number'] = $demandWorkItemsData->where('isEnd', false)->count(); // 需求未完成数量
        $iterationExtends['demand_complete_number'] = $demandWorkItemsData->where('isEnd', true)->count();    // 需求已完成数量

        $defectWorkItemsData = $workItemsData->where('cnt_type', WorkItemsModel::CNT_TYPE_FLAW);
        $iterationExtends['defect_number'] = $defectWorkItemsData->count();                                   // 缺陷数
        $iterationExtends['defect_incomplete_number'] = $defectWorkItemsData->where('isEnd', false)->count(); // 缺陷未完成数量
        $iterationExtends['defect_complete_number'] = $defectWorkItemsData->where('isEnd', true)->count();    // 缺陷已完成数量

        $taskWorkItemsData = $workItemsData->where('cnt_type', WorkItemsModel::CNT_TYPE_TASK);
        $iterationExtends['task_number'] = $taskWorkItemsData->count();                                   // 任务数
        $iterationExtends['task_incomplete_number'] = $taskWorkItemsData->where('isEnd', false)->count(); // 任务未完成数量
        $iterationExtends['task_complete_number'] = $taskWorkItemsData->where('isEnd', true)->count();    // 任务已完成数量

        return $iterationExtends;
    }

    /**
     * 判断是否当前节点
     * @param $cond
     * @return bool
     * User Long
     * Date 2024/12/26
     */
    private static function isPresentNode($cond): bool
    {
        return $cond == 'present_node';
    }

    /**
     * 判断是否未开始
     * @param $status
     * @return bool
     * User Long
     * Date 2024/12/26
     */
    private static function isStatusNotStarted($status): bool
    {
        return $status < IterationProcessNodeModel::STATUS_UNDER_WAY;
    }

    /**
     * 校验自动化数据
     * @param $res
     * @param $iterationExtends
     * @return array
     * User Long
     * Date 2024/12/14
     */
    private static function verifyAutoData($res, $iterationExtends): array
    {
        foreach ($res['node_data']['node_setting']['conditions'] as $a_key => &$condition) {
            // 设置默认自动化条件文案
            $res['conditions'][$a_key]['text'] = '';

            // 定义单组通过，默认值为 null
            $bKeyIsAutoStatus[$a_key]['is_auto_status'] = null;

            // 设置默认自动化条件状态
            if (!isset($res['conditions'][$a_key]['is_auto_status'])){
                $res['conditions'][$a_key]['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_ACQUIESCE;
            }

            // 自动化条件总数
            $res['conditions'][$a_key]['total'] = 0;

            // 处理自动化逻辑
            foreach ($condition as $b_key => &$condition_data) {
                foreach ($condition_data as $c_key => &$condition_data_data) {
                    // 拼接展示文案
                    $res['conditions'][$a_key]['text'] .= ($c_key > 0 ? '|' : '');
                    $res['conditions'][$a_key]['text'] .= ($b_key > 0 && $c_key == 0 ? '|' : '');
                    $res['conditions'][$a_key]['text'] .= ($condition_data_data['text'][0] ?? '');
                    $res['conditions'][$a_key]['text'] .= ($condition_data_data['text'][1] ?? '');

                    // 处理迭代节点回显
                    if (self::isPresentNode($condition_data_data['cond'])){
                        $presentNodeValue = is_array($condition_data_data['value']) ? $condition_data_data['value'] : [ $condition_data_data['value'] ];
                        $str = self::findNodeName($res['iteration_id'], $presentNodeValue);

                        $res['conditions'][$a_key]['text'] .= '【' . implode(', ', $str) . '】';
                    } else {
                        $res['conditions'][$a_key]['text'] .= !is_array($condition_data_data['value']) ? $condition_data_data['value'] : '';
                    }

                    // 默认为未通过
                    $condition_data_data['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN;

                    if (isset($iterationExtends[$condition_data_data['cond']])){
                        // 判断是否为当前节点, 并且未开始
                        if (self::isPresentNode($condition_data_data['cond']) && self::isStatusNotStarted($res['status'])){
                            $condition_data_data['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN;
                            $res['conditions'][$a_key]['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN;
                        } else {
                            switch ($condition_data_data['op']) {
                                case FlowProcessAutoLogic::EQ['value']:
                                    if ($iterationExtends[$condition_data_data['cond']] == $condition_data_data['value']){
                                        $condition_data_data['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_PASS;
                                    } else {
                                        $condition_data_data['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN;
                                        $res['conditions'][$a_key]['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN;
                                    }
                                    break;
                                case FlowProcessAutoLogic::NEQ['value']:
                                    if ($iterationExtends[$condition_data_data['cond']] != $condition_data_data['value']){
                                        $condition_data_data['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_PASS;
                                    } else {
                                        $condition_data_data['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN;
                                        $res['conditions'][$a_key]['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN;
                                    }
                                    break;
                                case FlowProcessAutoLogic::GT['value']:
                                    if ($iterationExtends[$condition_data_data['cond']] > $condition_data_data['value']){
                                        $condition_data_data['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_PASS;
                                    } else {
                                        $condition_data_data['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN;
                                        $res['conditions'][$a_key]['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN;
                                    }
                                    break;
                                case FlowProcessAutoLogic::EGT['value']:
                                    if ($iterationExtends[$condition_data_data['cond']] < $condition_data_data['value']){
                                        $condition_data_data['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_PASS;
                                    } else {
                                        $condition_data_data['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN;
                                        $res['conditions'][$a_key]['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN;
                                    }
                                    break;
                                case FlowProcessAutoLogic::IN['value']:
                                    if (in_array($iterationExtends[$condition_data_data['cond']], (
                                    is_array($condition_data_data['value'])
                                        ? $condition_data_data['value']
                                        : [ $condition_data_data['value'] ]
                                    ))
                                    ){
                                        $condition_data_data['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_PASS;
                                    } else {
                                        $condition_data_data['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN;
                                        $res['conditions'][$a_key]['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN;
                                    }
                                    break;
                                case FlowProcessAutoLogic::NOTIN['value']:
                                    if (!in_array($iterationExtends[$condition_data_data['cond']], (
                                    is_array($condition_data_data['value'])
                                        ? $condition_data_data['value']
                                        : [ $condition_data_data['value'] ]
                                    ))
                                    ){
                                        $condition_data_data['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_PASS;
                                    } else {
                                        $condition_data_data['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN;
                                        $res['conditions'][$a_key]['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN;
                                    }
                                    break;
                                default:
                                    break;
                            }
                        }
                    } else {
                        $res['conditions'][$a_key]['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN;
                    }

                    // 自动化状态文案
                    $condition_data_data['is_auto_text'] = IterationProcessNodeModel::IS_AUTO_STATUS_TEXT[$condition_data_data['is_auto_status']];

                    // 自动化条件总数
                    $res['conditions'][$a_key]['total'] += 1;
                }

                // 一整组自动化内有一组成功了，则当前条件自动通过
                if (!in_array(IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN, array_column($condition_data, 'is_auto_status'))){
                    if (!isset($bKeyIsAutoStatus[$a_key]['is_auto_status'])){
                        $bKeyIsAutoStatus[$a_key]['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_PASS;
                    }
                }
            }

            // 判断单组条件是否定义过 通过，非 null 则通过
            if (isset($bKeyIsAutoStatus[$a_key]['is_auto_status'])){
                $res['conditions'][$a_key]['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_PASS;
            }

            // 循环结束状态没变成失败，则当前条件自动通过
            if ($res['conditions'][$a_key]['is_auto_status'] === IterationProcessNodeModel::IS_AUTO_STATUS_ACQUIESCE){
                $res['conditions'][$a_key]['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_PASS;
            }

            // 状态文案
            $res['conditions'][$a_key]['is_auto_text'] = IterationProcessNodeModel::IS_AUTO_STATUS_TEXT[$res['conditions'][$a_key]['is_auto_status']];

            // 自动化条件列表
            $res['conditions'][$a_key]['auto_list'] = $condition;
        }
        // 删除引用
        unset($condition, $condition_data, $condition_data_data);

        // 自动化条件存在失败者修改状态未失败
        if (in_array(IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN, array_column($res['conditions'], 'is_auto_status'))){
            $res['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN;
        }

        return $res;
    }

    /**
     * 判断前置节点是否完成
     * @param int $iterationProcessNodeId
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/12/5
     */
    private static function isCompletePrevNode(int $iterationProcessNodeId)
    {
        $prevNodeRelationData = IterationProcessNodeRelationLogic::getPrevRelationDataByNodeId([ $iterationProcessNodeId ]);

        if (!$prevNodeRelationData->isEmpty()){
            $prevNodeModel = IterationProcessNodeModel::status()->whereIn('iteration_process_node_id', $prevNodeRelationData->column('process_node_id'))->select();

            foreach ($prevNodeModel as $prevNode) {
                if ($prevNode->status != IterationProcessNodeModel::STATUS_COMPLETED){
                    throw new BusinessException('前置节点未完成，不可点击');
                }

            }
        }
    }

    /**
     * 更新开始节点状态
     * @param int $iterationId
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/21
     */
    private static function updateStartNode(int $iterationId)
    {
        // 更新迭代开始时间
        $iterationModel = IterationModel::status()->where([ 'iteration_id' => $iterationId ])->find();

        if ($iterationModel->start_time == BaseModel::DEFAULT_TIME || !$iterationModel->start_time){
            $iterationModel->start_time = date('Y-m-d H:i:s');
            $iterationModel->extends = array_merge($iterationModel->extends, [ 'actual_iteration_cycle' => [ 0 => date('Y-m-d H:i:s'), 1 => '' ] ]);
            $iterationModel->save();
        }
    }

    /**
     * 更新结束节点状态
     * @param int $iterationId
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/20
     */
    private static function updateEndNode($iterationProcessNodeModel)
    {
        $iterationId = $iterationProcessNodeModel->iteration_id;
        $endNodeModel = IterationProcessNodeModel::status()->where([ 'iteration_id' => $iterationId, 'row_id' => IterationProcessNodeModel::END_NODE ])->find();

        $prevNodeRelationData = IterationProcessNodeRelationLogic::getPrevRelationDataByNodeId([ $endNodeModel->iteration_process_node_id ]);
        if (!$prevNodeRelationData->isEmpty()){
            $prevNodeModel = IterationProcessNodeModel::status()->whereIn('iteration_process_node_id', $prevNodeRelationData->column('process_node_id'))->select();

            // 判断结束节点前是否所有前置节点都完成，是的话修改结束节点状态
            if ($prevNodeModel->count() == $prevNodeModel->where('status', '=', IterationProcessNodeModel::STATUS_COMPLETED)->count()){
                $iterationModel = IterationModel::status()->where([ 'iteration_id' => $iterationId ])->find();
                $textStatus = FlowStatusTextLogic::findDataByFlowTextId((int)$endNodeModel->status_text_id);

                // 迭代实际周期
                $extends = $iterationModel->extends;
                $extends['actual_iteration_cycle'][0] = $extends['actual_iteration_cycle'][0] ?? '';
                $extends['actual_iteration_cycle'][1] = date('Y-m-d H:i:s');

                // 更新迭代状态
                if ($textStatus && $iterationModel->flow_status_id == $textStatus->flow_status_id){
                    $iterationModel->status_text_id = $textStatus->status_text_id;
                    $iterationModel->status_enum_id = $textStatus->status_enum_id;

                    $extends['flow_status_id'] = $textStatus->flow_status_id;
                    $extends['status_text_id'] = $textStatus->status_text_id;
                    $extends['status_enum_id'] = $textStatus->status_enum_id;

                    //写操作日志
                    IterationLogModel::changeIterationStatusLog($iterationProcessNodeModel, $iterationModel, $textStatus->status_enum_id);
                }

                $extends = (new self())->updateExtendsUpdateUser($extends);
                $iterationModel->extends = $extends;
                // 更新迭代结束时间
                $iterationModel->end_time = date('Y-m-d H:i:s');
                $iterationModel->save();

                $endNodeModel->status = IterationProcessNodeModel::STATUS_COMPLETED;
                $endNodeModel->start_time = $endNodeModel->end_time = date('Y-m-d H:i:s');

                $endNodeModel->save();
            }
        }
    }

    /**
     * 更新数据
     * @param int $iterationProcessNodeId
     * @param array $params
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/14
     */
    private function updateData(int $iterationProcessNodeId, array $params): void
    {
        $iterationProcessNodeModel = IterationProcessNodeModel::findById($iterationProcessNodeId);
        if (!$iterationProcessNodeModel){
            throw new NotFoundException();
        }

        $iterationProcessNodeModel->save($params);
    }

    /**
     * 根据 迭代id 原节点Id 获取流程节点名称
     * @param $iterationId
     * @param $processNodeIds
     * @return mixed
     * User Long
     * Date 2024/12/25
     */
    public static function findNodeName($iterationId, $processNodeIds)
    {
        return IterationProcessNodeModel::where([ 'iteration_id' => $iterationId ])
            ->whereIn('process_node_id', $processNodeIds)
            ->column('node_name');
    }

    /**
     * 根据 迭代id 获取流程节点
     * @param int $iterationId
     * @return array
     * User Long
     * Date 2024/11/13
     */
    public static function selectRelationDataByStatusId(int $iterationId)
    {
        return IterationProcessNodeModel::where([ 'iteration_id' => $iterationId ])->column('iteration_process_node_id', 'process_node_id');
    }

    /**
     * 复制 迭代与节点关系
     * @param $nodeModel
     * @param $flowStatusNodeModel
     * @param int $iterationId
     * @param int $projectId
     * @param array $extends
     * @throws \Throwable
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/9
     */
    public static function copyNodeData($nodeModel, $flowStatusNodeModel, int $iterationId, int $projectId, array $extends)
    {
        foreach ($nodeModel as $oldDatum) {
            $param = [];

            // 复制类别
            $param['iteration_id'] = $iterationId;      // 迭代id
            $param['node_name'] = $oldDatum->node_name; // 节点名称
            $param['node_data'] = $oldDatum->node_data; // 节点数据
            $param['row_id'] = $oldDatum->row_id;       // 节点标识

            // 当角色权限为个人时，清空角色数据
            if (isset($param['node_data']['node_manger']['type']) && $param['node_data']['node_manger']['type'] == 'personal'){
                $param['node_data']['node_manger']['roles'] = [];
            }

            // 更新迭代预设的人员角色数据
            if (empty($param['node_data']['node_manger']['users']) && isset($param['node_data']['node_manger']['type'])){
                $param['node_data']['node_manger']['users'] = (new IterationCatalogLogic())->getDefaultExtendsData($extends, $param['node_data']['node_manger']['roles'] ?? []);
            }

            // 节点标识为开始节点，则默认为已完成
            if ($param['row_id'] == IterationProcessNodeModel::START_NODE){
                $param['status'] = IterationProcessNodeModel::STATUS_COMPLETED;
                $param['start_time'] = $param['end_time'] = date('Y-m-d H:i:s');
            }

            $param['process_node_id'] = $oldDatum->process_node_id;                                                                                  // 流程Id
            $param['status_text_id'] = $flowStatusNodeModel->where('process_node_id', $oldDatum->process_node_id)->column('status_text_id')[0] ?? 0; // 流转状态描述id

            $iterationProcessNodeModel = new IterationProcessNodeModel();
            $iterationProcessNodeModel->save($param);

            if (!empty($oldDatum->node_data['node_setting']['tasks'])){
                foreach ($oldDatum->node_data['node_setting']['tasks'] as $task) {
                    // 获取可用类型id
                    $type_id = (new ProjectCategorySettingsLogic(BaseModel::SETTING_TYPE_TASK))->getAvailableCategoryIdByCategoryId($projectId, $task['type_id']);

                    // 创建迭代任务
                    (new WorkItemsLogic())->create([
                        'project_id'                              => $projectId,
                        'iteration_id'                            => $iterationId,
                        'cnt_type'                                => WorkItemsModel::CNT_TYPE_TASK,
                        'title'                                   => $task['title'],
                        'type_id'                                 => $type_id,
                        'handler_uid'                             => $param['node_data']['node_manger']['users'],
                        'iteration_process_node_id'               => $iterationProcessNodeModel->iteration_process_node_id,
                        'deliverables_to_be_submitted'            => isset($task['need_file']) && $task['need_file'], // 需提交交付物
                        'only_transferable_after_completion'      => isset($task['need_finish']) && $task['need_finish'], // 任务是否完成才可流转
                        'meeting_required'                        => isset($task['need_meeting']) && $task['need_meeting'], // 需要会议
                        'is_task_automatically_created_by_a_node' => true, // 是否是节点自动创建的任务
                    ]);
                }
            }
        }
    }

    /**
     * 流程节点信息 - 迭代目标
     * @param int $iterationId
     * @return IterationProcessNodeModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/15
     */
    public static function selectProcessNodeByIterationId(int $iterationId)
    {
        return IterationProcessNodeModel::status()
            ->where([ 'iteration_id' => $iterationId ])
            ->whereNotIn('row_id', [ IterationProcessNodeModel::START_NODE, IterationProcessNodeModel::END_NODE ])
            ->field('iteration_process_node_id, node_name, node_data, status, estimate_start_time, estimate_end_time, start_time, end_time')
            ->fieldRaw('1 as _is_time_convert') // 添加 _is_time_convert 字段，值为 1，用来判断修改器的时间格式
            ->order('status DESC, estimate_start_time ASC')
            ->append([ 'status_text' ])
            ->select();
    }

    /**
     * 获取流程节点
     * @param int $iterationId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/12
     */
    public static function getProcessNodeByIterationId(int $iterationId)
    {
        $res = [];
        $data = IterationProcessNodeModel::status()
            ->with([
                'tip' => function ($sql) {
                    $sql->field('iteration_process_node_tip_id, iteration_process_node_id, create_at, title, content');
                }
            ])
            ->where([ 'iteration_id' => $iterationId ])
            ->field('iteration_process_node_id, node_data, process_node_id, node_name, row_id, status, estimate_start_time, estimate_end_time, start_time, end_time')
            ->select();

        if ($data->isEmpty()) return $res;

        $prevRelationData = IterationProcessNodeRelationLogic::getPrevRelationDataByNodeId($data->column('iteration_process_node_id'));
        $nextRelationData = IterationProcessNodeRelationLogic::getNextRelationDataByNodeId($data->column('iteration_process_node_id'));

        return self::formatNodeList($data, $prevRelationData, $nextRelationData);
    }

    /**
     * 详情
     * @param int $iterationProcessNodeId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/13
     */
    public static function detail(int $iterationProcessNodeId): array
    {
        $model = IterationProcessNodeModel::status()
            ->field('*')
            ->where([ 'iteration_process_node_id' => $iterationProcessNodeId ])
            ->fieldRaw('1 as _is_time_convert') // 添加 _is_time_convert 字段，值为 1，用来判断修改器的时间格式
            ->find();

        if (!$model){
            throw new NotFoundException();
        }

        $res = $model->toDetail()->toArray();

        $iterationModel = IterationModel::findById($res['iteration_id']);
        if (!$iterationModel){
            throw new NotFoundException();
        }

        // 预设参数
        $res['is_turnover'] = 1;                                       // 是否可以开始节点 0 否 1 是
        $res['node_data']['advanced']['users_info'] = [];              // 删除人
        $res['node_data']['node_manger']['users_info'] = [];           // 负责人
        $res['node_data']['node_setting']['audit']['users_info'] = []; // 审核人
        $res['flow_process_id'] = $iterationModel->flow_process_id;    // 迭代流程id

        // 获取所有人员id
        $advancedUserIds = $res['node_data']['advanced']['users'] ?? [];
        $nodeMangerUserIds = $res['node_data']['node_manger']['users'] ?? [];
        $auditUserIds = $res['node_data']['node_setting']['audit']['users'] ?? [];
        $userIds = array_merge($nodeMangerUserIds, $advancedUserIds, $auditUserIds);

        // 获取节点人员id，查询用户信息并赋值
        if ($userIds){
            $userInfo = ProjectUserLogic::getUserInfoByUserIds((int)$iterationModel->project_id, $userIds);
            foreach ($userInfo as $userData) {
                foreach ($advancedUserIds as $userId) {
                    if ($userData->user_id == $userId){
                        $res['node_data']['advanced']['users_info'][] = [
                            'label'    => spliceUserName(
                                $userData->en_user_name,
                                $userData->user_name,
                                $userData->position_name
                            ), 'value' => $userData->user_id
                        ];
                    }
                }

                foreach ($nodeMangerUserIds as $userId) {
                    if ($userData->user_id == $userId){
                        $res['node_data']['node_manger']['users_info'][] = [
                            'label'    => spliceUserName(
                                $userData->en_user_name,
                                $userData->user_name,
                                $userData->position_name
                            ), 'value' => $userData->user_id
                        ];
                    }
                }

                if(in_array($userData->user_id,$auditUserIds)){
                    $res['node_data']['node_setting']['audit']['users_info'][] = [
                        'label'    => spliceUserName(
                            $userData->en_user_name,
                            $userData->user_name,
                            $userData->position_name
                        ), 'value' => $userData->user_id
                    ];
                }
            }
        }

        // 自动化校验处理
        $res['conditions'] = [];
        if (!empty($res['node_data']['node_setting']['conditions'])){
            // 默认自动化条件通过
            $res['is_auto_status'] = IterationProcessNodeModel::IS_AUTO_STATUS_PASS;

            $iterationExtends = self::upkeepAutoData($iterationModel->extends, $res['iteration_id'], $iterationProcessNodeId);

            $res = self::verifyAutoData($res, $iterationExtends);

            // 更新自动化状态
            $model->is_auto_status = $res['is_auto_status'];
            $model->save();
        }

        // 显示审批记录
        $res['audit_list'] = [];
        if (isset($res['node_data']['node_setting']['audit']['need_audit']) && $res['node_data']['node_setting']['audit']['need_audit']){
            $res['audit_list'] = IterationProcessNodeAuditLogic::columnAuditListByNodeId($iterationProcessNodeId);
        }

        $prevNodeRelationData = IterationProcessNodeRelationLogic::getPrevRelationDataByNodeId([ $iterationProcessNodeId ]);
        if (!$prevNodeRelationData->isEmpty()){
            $prevNodeModel = IterationProcessNodeModel::status()->whereIn('iteration_process_node_id', $prevNodeRelationData->column('process_node_id'))->select();

            foreach ($prevNodeModel as $prevNode) {
                if ($prevNode->status != IterationProcessNodeModel::STATUS_COMPLETED){
                    $res['is_turnover'] = 0; // 是否可以开始节点 0 否 1 是
                }
            }
        }

        return $res;
    }

    /**
     * 更新
     * @param int $iterationProcessNodeId
     * @param array $params
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/14
     */
    public static function updateNode(int $iterationProcessNodeId, array $params)
    {
        (new self())->updateData($iterationProcessNodeId, $params);
    }

    /**
     * 开始节点
     * @param int $projectId
     * @param int $iterationProcessNodeId
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/14
     */
    public static function onNode(int $projectId, int $iterationProcessNodeId)
    {
        $iterationProcessNodeModel = IterationProcessNodeModel::findById($iterationProcessNodeId);
        if (!$iterationProcessNodeModel){
            throw new NotFoundException();
        }

        if (!self::isNodeButton($projectId, $iterationProcessNodeModel->node_data['node_manger']['users'] ?? [])){
            throw new BusinessException('只有负责人、迭代leader、项目经理、有权限人员才可点击');
        }

        // 判断是否需要预估时间
        if ($iterationProcessNodeModel->node_data['basic']['need_fill_time'] ?? 0){
            if (!$iterationProcessNodeModel->estimate_start_time || !$iterationProcessNodeModel->estimate_end_time){
                throw new BusinessException('请选择预估时间');
            }
        }

        // 非开始节点
        if ($iterationProcessNodeModel->row_id != IterationProcessNodeModel::START_NODE){
            // 判断前置节点是否完成
            self::isCompletePrevNode($iterationProcessNodeId);
        }

        try {
            Db::startTrans();
            // 更新开始节点状态
            self::updateStartNode((int)$iterationProcessNodeModel->iteration_id);

            $iterationProcessNodeModel->save([
                'status'     => IterationProcessNodeModel::STATUS_UNDER_WAY,
                'start_time' => date('Y-m-d H:i:s')
            ]);

            // 更新迭代状态
            if ($iterationProcessNodeModel->status_text_id){
                $iterationModel = IterationModel::findById($iterationProcessNodeModel->iteration_id);
                $textStatus = FlowStatusTextLogic::findDataByFlowTextId((int)$iterationProcessNodeModel->status_text_id);

                if ($textStatus && $iterationModel->flow_status_id == $textStatus->flow_status_id){
                    $iterationModel->status_text_id = $textStatus->status_text_id;
                    $iterationModel->status_enum_id = $textStatus->status_enum_id;

                    $iterationExtends = $iterationModel->extends;
                    $iterationExtends['flow_status_id'] = $textStatus->flow_status_id;
                    $iterationExtends['status_text_id'] = $textStatus->status_text_id;
                    $iterationExtends['status_enum_id'] = $textStatus->status_enum_id;
                    $iterationExtends = (new self())->updateExtendsUpdateUser($iterationExtends);
                    $iterationModel->extends = $iterationExtends;

                    $iterationModel->save();
                    //写操作日志
                    IterationLogModel::changeIterationStatusLog($iterationProcessNodeModel, $iterationModel, $textStatus->status_enum_id);
                }
            }

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollback();

            throw $e;
        }
    }

    /**
     * 跳过节点
     * @param int $projectId
     * @param int $iterationProcessNodeId
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/14
     */
    public static function skipNode(int $projectId, int $iterationProcessNodeId)
    {
        $iterationProcessNodeModel = IterationProcessNodeModel::findById($iterationProcessNodeId);
        if (!$iterationProcessNodeModel){
            throw new NotFoundException();
        }

        if ($iterationProcessNodeModel->node_data['advanced']['is_allow_delete'] ?? 0){
            if (!self::isNodeButton($projectId, $iterationProcessNodeModel->node_data['advanced']['users'] ?? [], $iterationProcessNodeModel->node_data['advanced']['roles'])){
                throw new BusinessException('无操作权限');
            }
        }

        // 非开始节点
        if ($iterationProcessNodeModel->row_id != IterationProcessNodeModel::START_NODE){
            // 判断前置节点是否完成
            self::isCompletePrevNode($iterationProcessNodeId);
        }

        try {
            Db::startTrans();

            $iterationProcessNodeModel->save([
                'status'     => IterationProcessNodeModel::STATUS_COMPLETED,
                'start_time' => date('Y-m-d H:i:s'),
                'end_time'   => date('Y-m-d H:i:s'),
            ]);

            // 更新开始节点状态
            self::updateStartNode((int)$iterationProcessNodeModel->iteration_id);

            // 更新结束节点状态
            self::updateEndNode($iterationProcessNodeModel);

            // 记录 跳过节点
            IterationProcessNodeTipLogic::skipNode($iterationProcessNodeId);

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollback();

            throw $e;
        }
    }

    /**
     * 完成节点
     * @param int $projectId
     * @param int $iterationProcessNodeId
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/14
     */
    public static function completeNode(int $projectId, int $iterationProcessNodeId)
    {
        $iterationProcessNodeModel = IterationProcessNodeModel::findById($iterationProcessNodeId);
        if (!$iterationProcessNodeModel){
            throw new NotFoundException();
        }

        $error_msg = '';

        // 获取未完成的任务id集合
        $cntIds = WorkItemsLogic::getIterationNodeTaskIdByNodeId($iterationProcessNodeId, false, true);
        if ($cntIds){
            $error_msg .= '节点任务';
        }

        if ($iterationProcessNodeModel->is_auto_status == IterationProcessNodeModel::IS_AUTO_STATUS_TURN_DOWN){
            $error_msg .= ($error_msg ? '、' : '') . '节点自动化校验';
        }

        if (isset($iterationProcessNodeModel->node_data['node_setting']['audit']['need_audit'])
            && $iterationProcessNodeModel->node_data['node_setting']['audit']['need_audit']
            && in_array($iterationProcessNodeModel->is_audit_status, [
                IterationProcessNodeModel::IS_AUDIT_STATUS_ACQUIESCE,
                IterationProcessNodeModel::IS_AUDIT_STATUS_TURN_DOWN,
                IterationProcessNodeModel::IS_AUDIT_STATUS_PROCEED
            ])){
            $error_msg .= ($error_msg ? '、' : '') . '审批';
        }

        if ($error_msg){
            throw new BusinessException($error_msg . ' 未完成');
        }

        if (!self::isNodeButton($projectId, $iterationProcessNodeModel->node_data['node_manger']['users'] ?? [])){
            throw new BusinessException('只有负责人、迭代leader、项目经理、有权限人员才可点击');
        }

        try {
            Db::startTrans();
            $iterationProcessNodeModel->save([
                'status'   => IterationProcessNodeModel::STATUS_COMPLETED,
                'end_time' => date('Y-m-d H:i:s'),
            ]);

            // 更新结束节点状态
            self::updateEndNode($iterationProcessNodeModel);

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollback();

            throw $e;
        }
    }

    /**
     * 迭代节点下拉接口
     * @param int $iterationId
     * @return array [node_name=>迭代节点名称， process_node_id=>模版节点 id, iteration_process_node_id =>迭代节点 id]
     * User Long
     * Date 2024/12/26
     */
    public static function selectorProcessNode(int $iterationId): array
    {
        return IterationProcessNodeModel::status()->where([ 'iteration_id' => $iterationId ])
            ->column('node_name as label, process_node_id as value, iteration_process_node_id');
    }

    /**
     * 根据节点id获取迭代id
     * @param int $iterationProcessId
     * @return mixed
     * User Long
     * Date 2025/2/14
     */
    public static function getIterationIdByNodeId(int $iterationProcessId)
    {
        return IterationProcessNodeModel::status()->where([ 'iteration_process_node_id' => $iterationProcessId ])->value('iteration_id');
    }

    /**
     * 获取迭代中优先级最高的节点
     * 优先查找进行中的节点，如果没有进行中节点，则查找已完成节点中优先级最高的节点
     * 对于已完成节点，只查找最大层级(最靠近末端)的节点
     * 用于处理上一节点已完成、下一节点未开始的情况
     *
     * @param int $iterationId 迭代ID
     * @param bool $includeCompletedNodesIfNoInProgress 当没有进行中节点时，是否包含已完成节点，默认为true
     * @return IterationProcessNodeModel|null 优先级最高的节点模型，如果没有则返回null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2025/3/18
     */
    public static function getHighestPriorityInProgressNode(int $iterationId, bool $includeCompletedNodesIfNoInProgress = true)
    {
        // 获取标签库中的阶段标签，用于确定优先级
        $phaseTagsLogic = new TagLibraryLogic(TagLibraryModel::GROUP_TYPE_PHASE);
        $phaseTags = $phaseTagsLogic->getSelector();

        // 将阶段标签按优先级索引
        $priorityMap = [];
        foreach ($phaseTags as $tag) {
            if (isset($tag['priority'])){
                $priorityMap[$tag['value']] = intval($tag['priority']);
            }
        }

        // 首先查找进行中状态的节点
        $inProgressNodes = IterationProcessNodeModel::status()
            ->where([
                'iteration_id' => $iterationId,
                'status'       => IterationProcessNodeModel::STATUS_UNDER_WAY // 进行中状态
            ])
            ->whereNotIn('row_id', [ IterationProcessNodeModel::START_NODE, IterationProcessNodeModel::END_NODE ]) // 排除开始和结束节点
            ->select();

        // 如果有进行中的节点，则返回其中优先级最高的
        if (!$inProgressNodes->isEmpty()){
            return self::findHighestPriorityNode($inProgressNodes, $priorityMap);
        }

        // 如果没有进行中节点，且允许查找已完成节点，则查找最大层级的已完成节点中优先级最高的
        if ($includeCompletedNodesIfNoInProgress){
            // 获取所有已完成节点
            $completedNodes = IterationProcessNodeModel::status()
                ->where([
                    'iteration_id' => $iterationId,
                    'status'       => IterationProcessNodeModel::STATUS_COMPLETED // 已完成状态
                ])
                ->whereNotIn('row_id', [ IterationProcessNodeModel::START_NODE, IterationProcessNodeModel::END_NODE ]) // 排除开始和结束节点
                ->select();

            if (!$completedNodes->isEmpty()){
                // 获取所有节点之间的关系
                $nextRelationData = IterationProcessNodeRelationLogic::getNextRelationDataByNodeId($completedNodes->column('iteration_process_node_id'));

                // 筛选出最大层级节点（没有后继节点或其后继节点未完成的节点）
                $maxLevelNodes = [];

                foreach ($completedNodes as $node) {
                    $nodeId = $node->iteration_process_node_id;
                    $hasCompletedNextNode = false;

                    // 查找此节点的所有后继节点
                    $nextNodeIds = $nextRelationData->where('process_node_id', $nodeId)->column('next_node_id');

                    // 如果没有后继节点，则为最大层级节点
                    if (empty($nextNodeIds)){
                        $maxLevelNodes[] = $node;
                        continue;
                    }

                    // 检查所有后继节点是否已完成
                    foreach ($nextNodeIds as $nextNodeId) {
                        $nextNode = $completedNodes->where('iteration_process_node_id', $nextNodeId)->first();
                        // 如果有后继节点且已完成，则当前节点不是最大层级
                        if ($nextNode){
                            $hasCompletedNextNode = true;
                            break;
                        }
                    }

                    // 如果没有已完成的后继节点，则为最大层级节点
                    if (!$hasCompletedNextNode){
                        $maxLevelNodes[] = $node;
                    }
                }

                // 将最大层级节点转换为集合
                $maxLevelNodesCollection = new \think\Collection($maxLevelNodes);

                if (!$maxLevelNodesCollection->isEmpty()){
                    return self::findHighestPriorityNode($maxLevelNodesCollection, $priorityMap);
                }
            }
        }

        // 如果没有找到符合条件的节点，返回null
        return null;
    }

    /**
     * 在节点集合中查找优先级最高的节点
     *
     * @param \think\Collection $nodes 节点集合
     * @param array $priorityMap 优先级映射表
     * @return IterationProcessNodeModel|null 优先级最高的节点
     */
    private static function findHighestPriorityNode($nodes, $priorityMap)
    {
        // 如果没有优先级映射，则返回第一个节点
        if (empty($priorityMap)){
            return $nodes[0];
        }

        // 为每个节点分配优先级
        $nodePriorities = [];
        foreach ($nodes as $node) {
            // 获取节点的阶段值
            $nodeStage = '';
            if (isset($node->node_data['node_setting']['node_stage'])){
                $nodeStage = $node->node_data['node_setting']['node_stage'];
            }

            // 如果节点阶段在优先级映射中存在，则使用对应优先级，否则使用最低优先级
            $priority = $priorityMap[$nodeStage] ?? PHP_INT_MAX;
            $nodePriorities[$node->iteration_process_node_id] = $priority;
        }

        // 按优先级排序节点（优先级值越小，优先级越高）
        asort($nodePriorities);

        // 获取优先级最高的节点ID
        $highestPriorityNodeId = key($nodePriorities);

        // 返回优先级最高的节点
        return $nodes->where('iteration_process_node_id', $highestPriorityNodeId)->first();
    }
}

