<?php
/**
 * Desc 示例 - 模型
 * User
 * Date 2024/08/26*/

declare (strict_types=1);

namespace app\work_items\model;

use app\infrastructure\model\EnumModel;
use app\infrastructure\model\FieldConfigModel;
use app\iterate\model\FlowStatusEnumModel;
use app\project\logic\ProjectUserRoleLogic;
use app\project\model\IterationProcessNodeModel;
use app\work_items\logic\IterationDemandRelationRecordsLogic;
use app\work_items\logic\PlanUseCaseLogic;
use app\work_items\logic\TestPlanWorkCaseLogic;
use app\work_items\logic\WorkItemsEsLogic;
use app\work_items\logic\WorkItemsLogic;
use basic\BaseModel;
use exception\NotFoundException;
use exception\ParamsException;
use think\Model;
use think\model\relation\HasMany;
use traits\OptimLockTrait;
use utils\Ctx;
use utils\DBTransaction;
use utils\Middleground;

/**
 * This is the model class for table "iteration_content".
 * @property int $cnt_id   id
 * @property string $is_delete
 * @property array $extends  自定义字段
 * @property string $contents 内容
 * @property string $cnt_type 类型;1-需求,2-任务,3-缺陷
 * @property string $version  版本号
 */
class WorkItemsModel extends BaseModel
{
    use OptimLockTrait;

//    use OperationLogTrait;

    protected $pk   = 'cnt_id';
    protected $name = 'work_items';


    public function setExtendsAttr($value, $data)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    public function getExtendsAttr($value, $data)
    {

        return json_decode($value ?? '{}', true);
    }

    const CNT_TYPE_DEMAND = 1;//类型-需求
    const CNT_TYPE_TASK   = 2;//类型-任务
    const CNT_TYPE_FLAW   = 3;//类型-缺陷


    //冗余在mysql中的字段，contents不存es中
    const REDUNDANT_FIELDS
        = [
            'contents'  => '',
            'cnt_type'  => 0,
            'is_delete' => 0,
            'version'   => 0,
        ];

    protected array $logFieldList
        = [
//        'microservice_name' => '终端名称',
//        //        'extends' => [
//        //            'type' => 'json',
//        //        ],
//        'is_enable' => [
//            'type' => "enum",
//            'field_label' => '状态',
//            'values' => [
//                self::ENABLE_YES => '开启',
//                self::ENABLE_NOT => '关闭',
//            ]
//        ],

        ];

    public static function findById($id)
    {
        return static::where([ 'cnt_id' => $id, 'is_delete' => self::DELETE_NOT ])->find();
    }

    public static function findListById($id)
    {
        return static::where([ 'cnt_id' => $id, 'is_delete' => self::DELETE_NOT ])->select();
    }

    /**
     * 查询需求集合，并获取testCaseWorkItems
     * @param $idList
     * @return WorkItemsModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/10/10 11:05
     */
    public static function findDemandByIdListWithTestCase($idList)
    {
        return static::where([
            'cnt_id'    => $idList,
            'is_delete' => self::DELETE_NOT,
            'cnt_type'  => self::CNT_TYPE_DEMAND
        ])
            ->with([ 'testCaseWork' ])
            ->select();
    }


    /**
     * @return string[]
     */
    public function getLogFieldList(): array
    {
        return $this->logFieldList;
    }


    public function toDetail()
    {
        $this->refresh();
        $data = $this->getData();
        unset($data['extends']);

        $result = array_merge($this->extends, $data);
        $result['is_set_iteration'] = (new WorkItemsLogic)->isSetIteration($result);

        if ($result['iteration_process_node_id'] ?? false){
            $processNode = IterationProcessNodeModel::findById($result['iteration_process_node_id']);
            if (!$processNode){
                throw new NotFoundException("流程节点不存在");
            }
            $result['iteration_process_node_name'] = $processNode->node_name;
        }
        $result = [ $result ];
        WorkItemsEsLogic::getInstance()->mergeParnetName($result);

        return $result[0];
    }


    /**
     * 返回所有组件
     * @return FieldConfigModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/8/27 21:28
     */
    public function getFieldList()
    {
//        $fieldNameList = array_keys($this->extends);
        return FieldConfigModel::getListByFieldName(null, $this->getModuleIdByCntType(), $this->extends['project_id']);
    }

    /**
     * 根据cnt_type获取模块id
     * @return int
     * <AUTHOR>
     * @date   2024/9/5 16:48
     */
    public function getModuleIdByCntType()
    {
        switch ($this->cnt_type) {
            case self::CNT_TYPE_DEMAND:
                return FieldConfigModel::MODULE_TYPE_REQUIREMENT;
            case self::CNT_TYPE_TASK:
                return FieldConfigModel::MODULE_TYPE_TASK;
            case self::CNT_TYPE_FLAW:
                return FieldConfigModel::MODULE_TYPE_DEFECT;
            default:
                throw new ParamsException("未匹配的cnt_type:" . $this->cnt_type);
        }
    }

    /**
     * 是否是需求
     * @return bool
     * <AUTHOR>
     * @date   2024/8/29 11:54
     */
    public function isDemand()
    {
        return $this->cnt_type == self::CNT_TYPE_DEMAND;
    }

    /**
     * 是否是任务
     * @return bool
     * <AUTHOR>
     * @date   2024/8/29 11:54
     */
    public function isTask()
    {
        return $this->cnt_type == self::CNT_TYPE_TASK;
    }

    /**
     * 是否是缺陷
     * @return bool
     * <AUTHOR>
     * @date   2024/8/29 11:54
     */
    public function isFlaw()
    {
        return $this->cnt_type == self::CNT_TYPE_FLAW;
    }

    /**
     * 获取所有冗余字段，有值就取值，无值用常量中的默认值
     * @return array
     * <AUTHOR>
     * @date   2024/9/5 11:14
     */
    public function getRedundantFields()
    {
        $oldData = $this->getData();
        $result = [];
        foreach (self::REDUNDANT_FIELDS as $k => $v) {
            $result[$k] = $oldData[$k] ?? $v;
        }

        return $result;
    }


    /**
     * 获取root_id,以及parents父级引用路径
     * @param $cntId
     * @param $parentId
     * @return mixed
     * <AUTHOR>
     * @date   2024/9/5 11:15
     */
    public function getRootIdAndParents($cntId, $parentId)
    {
        if (!$parentId){
            return [ 'root_id' => $cntId, 'parents' => [ $cntId ] ];
        }
        $parent = WorkItemsModel::findById($parentId);
        if (!$parent){
            throw new ParamsException("父级不存在或者已删除！");
        }
        if (in_array($cntId, $parent->extends['parents'])){
            throw new ParamsException("不可选择自身以及自身的后代节点为父级！");
        }

        //自身的parents = 父级parents追加自身
        return [ 'root_id' => $parent->extends['root_id'], 'parents' => array_merge($parent->extends['parents'], [ $cntId ]) ];

    }

    /**
     * 生成创建数据
     * @return array
     * <AUTHOR>
     * @date   2024/8/27 21:12
     */
    private function generationCreateData()
    {
        return [
            'create_by'      => Ctx::$user->userId,
            'create_by_name' => Ctx::$user->name,
            'create_at'      => date('Y-m-d H:i:s'),
        ];
    }

    /**
     * 生成修改数据
     * @return array
     * <AUTHOR>
     * @date   2024/8/27 21:12
     */
    private function generationUpdateData()
    {
        return [
            'update_by'      => Ctx::$user->userId,
            'update_by_name' => Ctx::$user->name,
            'update_at'      => date('Y-m-d H:i:s'),
        ];
    }

    /**
     * 唯一改变，data可为空
     * @param array $data
     * @param array $allowField
     * @param bool $replace
     * @param string $suffix
     * @return Model
     * <AUTHOR>
     * @date   2024/9/5 10:28
     */
    public static function create(array $data = [], array $allowField = [], bool $replace = false, string $suffix = ''): Model
    {
        return parent::create($data, $allowField, $replace, $suffix);
    }


    //递归结束是否自动执行保存
    public static $autoSave = true;

    //调用一次save需修改的所有数据
    public static $saveSql = [];

    //save的递归深度，当等于0时才会执行保存操作
    public static $saveDepth = 0;

    /**
     * 同步保存es,并更新工时
     * @param array $data
     * @param string|null $sequence
     * @return bool
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * <AUTHOR>
     * @date   2024/9/5 16:50
     */
    public function save(array $data = [], string $sequence = null, int $workCommentId = 0): bool
    {
        self::$saveDepth++;

        //生成cnt_id,不走下面逻辑
        if ($this->isEmpty()){
            self::$saveDepth--;
            return parent::save(array_merge(self::REDUNDANT_FIELDS, $data), $sequence);
        }
        $logic = (new WorkItemsLogic);


        //将extends字段覆盖冗余字段，保持一致
        $fields = $this->getRedundantFields();
        $saveData = array_replace($fields, array_intersect_key($data, $fields));

        //旧数据
        $oldData = json_decode($this->getData()['extends'] ?? '{}', true);

        //es中不保存contents、version
        unset($data['contents'], $data['version']);


        //创建0，修改1
        $operationType = !empty($this->extends);
        $data = $operationType ? array_merge($data, $this->generationUpdateData()) : array_merge($data, $this->generationCreateData());

        //root_id、parents
        if (array_key_exists('parent_id', $data)){
            //string转int类型
            $data['parent_id'] = is_numeric($data['parent_id']) ? (int)$data['parent_id'] : $data['parent_id'];
            //空用null表示，不用0
            $data['parent_id'] = $data['parent_id'] ?: null;
            $data = array_merge($data, $this->getRootIdAndParents($this->cnt_id, $data['parent_id']));
        }

        //mysql需保存修改的数据以及没改的数据，es只需要保存修改的数据
        $saveData['extends'] = $data+$oldData;
        $result = parent::save($saveData, $sequence);//保存mysql

        self::$saveSql[] = array_merge($data, [ $this->getPk() => $this->cnt_id ]);
        //修改后的联动数据处理
        if (array_key_exists('parent_id', $data)){
            $this->parentIdChanged($logic, $data, $oldData);
        }

        // 处理迭代关联记录
        if ($this->isDemand() && array_key_exists('iteration_id', $data)){
            // 记录需求关联到迭代的信息
            $oldIterationId = $oldData['iteration_id'] ?? 0;
            $newIterationId = $data['iteration_id'] ?? 0;

            // 只有当迭代ID有变化时才记录
            if ($oldIterationId != $newIterationId){
                (new IterationDemandRelationRecordsLogic())->create(
                    $this,
                    $data,
                    $oldIterationId
                );
            }

            $this->demandIterationIdChange();
        }

        if (($this->isDemand() || $this->isTask()) && array_key_exists('parent_id', $data)){
            $this->addSubset($data['parent_id'], $this->cnt_id, $this->cnt_type);
            !empty($oldData['parent_id']) && $oldData['parent_id'] != $data['parent_id'] && $this->subSubset($oldData['parent_id'], $this->cnt_id);
        }

        // 检测meeting_collection是否有改变，如果有，记录相关数据
        if ($this->isTask() && array_key_exists('meeting_collection', $data)){
            $this->recordMeetingCollectionChange($data, $oldData);
        }

        //删除时去除父级的子集属性
        if (($this->isDemand() || $this->isTask()) && ($data['is_delete'] ?? false)){

            $this->extends['parent_id'] && $this->subSubset($this->extends['parent_id'], $this->cnt_id);
        }


        if ($data['is_delete'] ?? false){
            //删除计划中的需求
            if ($this->isDemand()){
                (new TestPlanWorkCaseLogic)->del(null, null, $this->cnt_id);
            }
            //删除计划中的缺陷
            if ($this->isFlaw()){
                (new PlanUseCaseLogic())->delRecordBugByCntId($this->cnt_id);
            }
        }

        if ($this->isFlaw()){
            // 处理缺陷变更通知
            $this->changeOfDefectHandler($data, $oldData);

            if ($operationType){
                //缺陷修改日志
                unset($data['update_by'], $data['update_by_name'], $data['update_at'], $data['parents']);

                $this->logDefectChangeIfNeeded($workCommentId, $oldData, $data, $logic);
            }
        }


        self::$saveDepth--;
        if (self::$saveDepth == 0 && self::$autoSave){
            self::actualSaving();
        }
        return $result;
    }

    /**
     * 缺陷修改日志
     * @param array $data
     * @param array $oldData
     * @param WorkItemsLogic $logic
     * @exception Exception
     * <AUTHOR>
     * @date 2025/5/16 14:34
     */
    private function logDefectChangeIfNeeded(int $workCommentId, array $oldData, array $data, WorkItemsLogic $logic)
    {
        $diffArr = getSaveDifference($oldData, $data);
        if (!empty($diffArr)){

            $remark = "";
            if (!empty($diffArr['status_enum_id'])){
                $status = $diffArr['status_enum_id'];
                $statusColumn = FlowStatusEnumModel::whereIn('status_enum_id', array_filter(array_values($status)))->column('name', 'status_enum_id');
                $remark = "状态流转：" . (!empty($status['old']) ? $statusColumn[$status['old']] : "--") . " 至 " . (!empty($status['new']) ? $statusColumn[$status['new']] : "--");
                unset($diffArr['status_enum_id']);
            }

            $handleStr = $logic->assembleSaveDiffString((int)$oldData['project_id'], $diffArr);

            if ($workCommentId > 0){
                $workComment = WorkCommentModel::where('comment_id', $workCommentId)->find();
                $content = $handleStr;
                $content = str_replace("</p>", "", $content) . "<br /> 评论：" . str_replace([ '<p>', '</p>' ], "", $workComment->content) . " </p>";
                $workComment->save([
                    "content" => $content
                ]);
            } else {
                WorkCommentModel::create([
                    "comment_type"  => BaseModel::SETTING_TYPE_DEFECT,
                    "work_items_id" => $oldData['cnt_id'],
                    "content"       => $handleStr,
                    "remark"        => $remark
                ]);
            }

        }
    }


    /**
     * 处理缺陷变更通知
     * @param array $data 变更数据
     * @return void
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function changeOfDefectHandler($data, $oldData)
    {
        // 判断处理人是否变更
//        返回在 $data['handler_uid'] 中但是不在$oldData['handler_uid']里的值
        if (!isset($data['handler_uid']) || empty(array_diff($data['handler_uid'], ($oldData['handler_uid'] ?? [])))){
            return;
        }

        //且人员为"前后端开发（中台角色）"时，进行消息通知
        $receivers = [];
        ProjectUserRoleLogic::selectUserRole($data['handler_uid'])->each(function ($item) use (&$receivers) {
            if (in_array($item['project_role'], [
                EnumModel::BACKEND_DEVELOPMENT,
                EnumModel::WEB_DEVELOP,
                EnumModel::SOFTWARE_TEST,
                EnumModel::SOFTWARE_CSZZ,
            ])){
                $receivers[] = $item['user_id'];
            }
        });

        $receivers = array_unique($receivers);

        if (!$receivers){
            return;
        }

        if (env('APP_DEBUG', false)){
            $receivers[] = 307225;//测试环境，李粤湘
            $receivers[] = 307679;//测试环境，林俊良
            $receivers[] = 307567;//测试环境，袁志凡
            $receivers[] = 307622;//测试环境，袁梓博
        }



        // 如果有新的处理人,则发送通知
        if ($data['handler_uid']){
            Middleground::instance()->defectNotificationMessage($this->cnt_id, Ctx::$user->userId, $receivers);
        }
    }

    /**
     * 记录会议集合变更
     * @param array $data 新数据
     * @param array $oldData 旧数据
     * @return void
     * <AUTHOR>
     * @date   2024/12/10
     */
    private function recordMeetingCollectionChange($data, $oldData)
    {
        // 如果meeting_collection没有变化，直接返回
        if (isset($oldData['meeting_collection'])
            && json_encode($data['meeting_collection']) === json_encode($oldData['meeting_collection'])
        ){
            return;
        }

        // 获取项目信息
        $projectId = $this->extends['project_id'] ?? null;
        if (!$projectId){
            return;
        }

        // 获取任务信息
        $taskId = $this->cnt_id;
        $taskTitle = $this->extends['title'] ?? '';

        // 总是先删除所有历史记录
        $existingRecords = MeetingCollectionChangeModel::findByTaskId($taskId);
        foreach ($existingRecords as $record) {
            $record->save([ 'is_delete' => self::DELETE_YES ]);
        }

        $meetingCollection = $data['meeting_collection'] ?? [];
        if (empty($meetingCollection)){
            return; // 没有新的会议记录，处理完毕
        }

        // 根据新的 meeting_collection 重新创建所有记录
        try {
            // 开启事务
            DBTransaction::begin();

            // 获取节点负责人
            $nodeManagers = [];
            if ($iterationProcessNodeId = $this->extends['iteration_process_node_id'] ?? ''){
                // 获取迭代节点信息
                $iterationNode = IterationProcessNodeModel::findById($iterationProcessNodeId);
                $nodeManagers = $iterationNode['node_data']['node_manger']['users'] ?? [];
            }

            // 遍历每个会议记录创建对应的变更记录
            foreach ($meetingCollection as $meeting) {
                $meetingChange = new MeetingCollectionChangeModel();
                $meetingChange->project_id = $projectId;
                $meetingChange->task_id = $taskId;
                $meetingChange->task_title = $taskTitle;
                $meetingChange->is_delete = self::DELETE_NOT;
                $meetingChange->save();

                // 创建会议类型关联记录
                if (!empty($meeting['type'])){
                    $meetingTypes = [];
                    if (is_array($meeting['type'])){
                        // 处理多个会议类型
                        foreach ($meeting['type'] as $type) {
                            if (is_array($type) && isset($type['code']) && isset($type['name'])){
                                $meetingTypes[] = $type;
                            } else if (is_string($type)){
                                // 兼容旧格式，只有类型ID的情况
                                $meetingTypes[] = [ 'code' => $type, 'name' => '' ];
                            }
                        }
                    } else {
                        // 兼容旧格式，只有一个类型ID的情况
                        $meetingTypes[] = [ 'code' => $meeting['type'], 'name' => '' ];
                    }

                    MeetingCollectionChangeTypeModel::batchCreate($meetingChange->change_id, $meetingTypes);
                }

                // 创建节点负责人关联记录
                if (!empty($nodeManagers) && is_array($nodeManagers)){
                    MeetingCollectionChangeUserModel::batchCreate(
                        $meetingChange->change_id,
                        $nodeManagers,
                        MeetingCollectionChangeUserModel::USER_TYPE_NODE_MANAGER
                    );
                }

                // 创建主讲人关联记录
                if (!empty($meeting['speaker']) && is_array($meeting['speaker'])){
                    MeetingCollectionChangeUserModel::batchCreate(
                        $meetingChange->change_id,
                        $meeting['speaker'],
                        MeetingCollectionChangeUserModel::USER_TYPE_SPEAKER
                    );
                }
            }

            // 提交事务
            DBTransaction::commit();
        } catch (\Exception $e) {
            // 回滚事务
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 批量保存es
     * @return void
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     */
    static public function actualSaving()
    {
        if (!self::$saveSql){
            return;
        }
        WorkItemsEsLogic::getInstance()->bulk(self::$saveSql);
    }

    /**
     * 父级改变时的处理
     * 向上更新工时
     * 向下更新root_id、parents,如果是任务，还需更新迭代id为父级的迭代id
     * @param WorkItemsLogic $logic
     * @param                  $data
     * @param                  $oldData
     * @return void
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * <AUTHOR>
     * @date   2024/9/11 16:30
     */
    public function parentIdChanged(WorkItemsLogic $logic, $data, $oldData)
    {
        $newParentId = $data['parent_id'] ?? 0;
        $oldParentId = $oldData['parent_id'] ?? 0;

        if ($newParentId == $oldParentId){
            return;
        }

        if (isset($oldData['root_id'])){
            $tree = $logic->getTree($oldData['root_id']);
            $logic->recursiveChildren($tree, $this->cnt_id, function (self $model) {
                //原样不变的保存parent_id,目的是为了更新root_id,以及parents
                $model->save([ 'parent_id' => $model->extends['parent_id'] ]);
            });
        }

        if ($this->isTask()){
            $logic->updateWorkHours($newParentId);
            $logic->updateWorkHours($oldParentId);

            if ($this->extends['parent_id']){
                $parent = self::findById($this->extends['parent_id']);
                $this->save([ 'iteration_id' => $parent->extends['iteration_id'] ?? null ]);
            }
        }

        $parent = self::findById($this->extends['parent_id']);
        if ($parent && !empty($parent->extends['subset'])){
            if ($this->isTask() && $parent->extends['subset_cnt_type'] == self::CNT_TYPE_DEMAND){
                throw new ParamsException('父级已包含子需求,不可再添加子任务');
            }
        }


    }

    /**
     * 需求迭代改变需同步更改子集任务的迭代
     * @return void
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * <AUTHOR>
     * @date   2024/11/11 16:52
     */
    private function demandIterationIdChange()
    {
        if ($this->extends['subset'] ?? []){
            $subsetModelList = self::findListById($this->extends['subset']);
            if (in_array($this->extends['subset_cnt_type'], [ WorkItemsModel::CNT_TYPE_TASK, 0 ])){
                $subsetModelList->each(function (WorkItemsModel $model) {
                    $model->save([ 'iteration_id' => $this->extends['iteration_id'] ]);
                });
            }
        }
    }

    /**
     * 增加子集id
     * @param $parentId
     * @param $childId
     * @return void
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * <AUTHOR>
     * @date   2024/11/11 14:29
     */
    private function addSubset($parentId, $childId, $childCntType)
    {
        if (!$parentId){
            return;
        }
        $parent = self::findById($parentId);
        if (!$parent){
            throw new ParamsException("parent_id:{$parentId}不存在");
        }
        $subset = $parent->extends['subset'] ?? [];
        if (!in_array($childId, $subset)){

            $subset = array_merge($subset, [ $this->cnt_id ]);
            $saveData = [ 'subset' => $subset, 'subset_cnt_type' => $childCntType ];
            if ($childCntType == self::CNT_TYPE_DEMAND){
                //有子需求直接清楚迭代
                $saveData['iteration_id'] = null;
            }
            $parent->save($saveData);
        }
    }

    /**
     * 清除子集id
     * @param $parentId
     * @param $childId
     * @return void
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\MissingParameterException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     * <AUTHOR>
     * @date   2024/11/11 14:58
     */
    private function subSubset($parentId, $childId)
    {
        if (!$parentId){
            return;
        }
        $parent = self::findById($parentId);
        if (!$parent){
            return;
//            throw new ParamsException("parent_id:{$parentId}不存在");
        }
        $subset = $parent->extends['subset'] ?? [];
        if (in_array($childId, $subset)){
            $subset = array_values(array_diff($subset, [ $childId ]));

            if ($subset){
                $parent->save([ 'subset' => $subset ]);
            } else {
                $parent->save([ 'subset' => $subset, 'subset_cnt_type' => 0 ]);
            }
        }
    }

    public function testCaseWork(): HasMany
    {
        return $this->hasMany(TestCaseWorkModel::class, 'cnt_id');
    }


}
