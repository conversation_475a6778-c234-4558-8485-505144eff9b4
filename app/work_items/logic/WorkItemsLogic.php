<?php

declare(strict_types=1);

namespace app\work_items\logic;

use app\infrastructure\model\EnumModel;
use app\infrastructure\logic\EnumLogic;
use app\infrastructure\model\FieldConfigModel;
use app\infrastructure\model\FieldSubsetModel;
use app\iterate\logic\FlowStatusLogic;
use app\iterate\logic\FlowStatusTextLogic;
use app\iterate\model\FlowStatusEnumModel;
use app\iterate\model\FlowStatusTextModel;
use app\iterate\model\IterationModel;
use app\project\logic\IterationCatalogLogic;
use app\project\logic\ProjectCategorySettingsLogic;
use app\project\logic\ProjectUserLogic;
use app\project\model\IterationProcessNodeModel;
use app\project\model\ProjectCategorySettingsModel;
use app\project\model\ProjectModel;
use app\project\model\ProjectUserModel;
use app\project\scheduled_tasks\BugStatistics;
use app\work_items\model\TestCaseModel;
use app\work_items\model\TestPlanModel;
use app\work_items\model\WorkCommentModel;
use app\work_items\model\WorkHoursModel;
use app\work_items\model\WorkItemsModel;
use app\work_items\validate\WorkItemsValidate;
use basic\BaseLogic;
use basic\BaseModel;
use Closure;
use DateTime;
use Elastic\Elasticsearch\Exception\ClientResponseException;
use Elastic\Elasticsearch\Exception\MissingParameterException;
use Elastic\Elasticsearch\Exception\ServerResponseException;
use exception\NotFoundException;
use exception\ParamsException;
use field_utils\Transfer;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\facade\Db;
use think\Model;
use think\Paginator;
use think\paginator\driver\Bootstrap;
use Throwable;
use utils\Ctx;
use utils\DBTransaction;
use utils\Es;
use utils\Log;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use app\project\logic\CustomizeTheWorkingDayLogic;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Color;

class WorkItemsLogic extends BaseLogic
{


    private WorkItemsEsLogic $esLogic;

    public function __construct()
    {
        $this->esLogic = WorkItemsEsLogic::getInstance();
    }


    /**
     * 处理 工作项 与 类别 枚举定义类型不一致问题
     * @param $projectCategorySettingsType
     * @return int $cnt_type
     * User Long
     * Date 2024/9/4
     */
    private static function handleProjectCategorySettingsType($projectCategorySettingsType)
    {
        return match ($projectCategorySettingsType) {
            BaseModel::SETTING_TYPE_DEMAND => WorkItemsModel::CNT_TYPE_DEMAND,
            BaseModel::SETTING_TYPE_TASK => WorkItemsModel::CNT_TYPE_TASK,
            BaseModel::SETTING_TYPE_DEFECT => WorkItemsModel::CNT_TYPE_FLAW,
            default => 0,
        };
    }

    /**
     *  cntType=>settingsType
     * @param $cntType
     * @return int
     * <AUTHOR>
     * @date   2024/9/12 14:17
     */
    private function getSettingsTypeByCntType($cntType)
    {
        $settingsType = match ((int)$cntType) {
            WorkItemsModel::CNT_TYPE_DEMAND => BaseModel::SETTING_TYPE_DEMAND,
            WorkItemsModel::CNT_TYPE_TASK => BaseModel::SETTING_TYPE_TASK,
            WorkItemsModel::CNT_TYPE_FLAW => BaseModel::SETTING_TYPE_DEFECT,
            default => 0,
        };

        return $settingsType;
    }

    /**
     * 新增
     * @param $params
     * @return array|string
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function create($params)
    {
        validate(WorkItemsValidate::class)->scene('create')->check($params);

        DBTransaction::begin();
        try {
            $model = WorkItemsModel::create(
                ['cnt_type' => $params['cnt_type']]
            );
            $params['cnt_id'] = $model->cnt_id;
            $params['is_delete'] = WorkItemsModel::DELETE_NOT;
            if ( ! isset($params['parent_id'])) {
                $params['parent_id'] = 0;
            }

            //无category_id或为0都表示未分类,只有需求需要
            if ($model->isDemand() && ! ($params['category_id'] ?? false)) {
                $params['category_id'] = '-1';
            }

            if (isset($params['type_id'])) {
                $categorySettingsModel = ProjectCategorySettingsModel::findEnabledByCategoryId($this->getSettingsTypeByCntType($model->cnt_type), $params['type_id']);
                if ( ! $categorySettingsModel) {
                    throw new NotFoundException("类别不存在或已删除！");
                }
            }


            $this->checkTaskParentIterationUnanimous($model, $params);

            if ($model->isDemand() || $model->isFlaw()) {
                //通过类别id获取流程id、状态id
                $typeSettings
                    = ProjectCategorySettingsLogic::getDefaultFlowStatusByCategorySettingsId(
                    (int)$params['type_id']
                );
                $params['flow_status_id'] = $typeSettings['flowStatusId'];
                $params['status_enum_id'] = $typeSettings['statusEnumId'];
            } else { //任务
                $params['status_enum_id'] = EnumLogic::findEnumValue(
                    'notStarted'
                );
            }
            $params['isEnd'] = false;       //是否是结束状态
            $params['subset'] = [];         //子集（仅是子需求|子任务，无子缺陷）
            $params['subset_cnt_type'] = 0; //子集cntType，0表示无子集

            //查询排序值最大的+1
            $maxOrder = $this->esLogic->esSearch([], 1, 1, [], [
                'sort_order' => [
                    'order' => 'desc'
                ]
            ])->getCollection()->first();
            $maxOrder = $maxOrder ? $maxOrder['sort_order'] + 1 : 1;
            $params['sort_order'] = $maxOrder; //排序值

            $model->save($params);

            //设置预估工时
            $this->setTime($params);

            if ($model->isDemand() && $params['parent_id']) {

                $this->transferTasks($model->cnt_id, $params['parent_id']);
            }

            //缺陷转需求
            if ($model->isDemand() && ($params['flaw_id'] ?? 0)) {

                $flaw = WorkItemsModel::findById($params['flaw_id']);
                if ( ! $flaw) {
                    throw new Exception("缺陷不存在");
                }
                $flaw->save(['demand_id' => $model->cnt_id]);
                //复制评论
                (new CommentLogic)->copy($params['flaw_id'], $model->cnt_id);

                $this->setFlawStatusEnd($flaw);
            }


            DBTransaction::commit();

            //复现bug用
            if ( ! WorkItemsModel::findById($model->cnt_id)) {
                throw new Exception("创建失败，请联系开发！");
            }

            return $model->toDetail();
        } catch (Throwable $e) {
            //es不会回滚，所以手动删除已创建的数据
            $this->esLogic->deleteEsByQuery([
                ['field_name' => 'cnt_id', 'value' => $model->cnt_id, 'type' => 'term', 'operate_type' => 'equal'],
            ]);
            DBTransaction::rollback();
            throw  $e;
        }
    }


    /**
     * 检查任务的所属迭代和父需求所属迭代是否一致
     * @param  WorkItemsModel  $model
     * @param                  $params
     * @return void
     */
    private function checkTaskParentIterationUnanimous(WorkItemsModel $model, $params)
    {
        if ($model->isTask() && (isset($params['parent_id']) || isset($params['iteration_id']))) {
            $parent_id = $params['parent_id'] ?? $model->extends['parent_id'] ?? null;
            $iteration_id = $params['iteration_id'] ?? $model->extends['iteration_id'] ?? null;
            if ( ! $parent_id || ! $iteration_id) {
                return;
            }
            $parent = WorkItemsModel::findById($parent_id);
            if ( ! $parent) {
                throw new NotFoundException();
            }

            if (($parent->extends['iteration_id'] ?? null) != $iteration_id) {
                throw new ParamsException("任务迭代需和父需求迭代一致！");
            }
        }
    }

    /**
     * 根据id保存es数据
     * @param                  $id
     * @param                  $data
     * @param  int             $operationType  操作类型0创建，1更新
     * @param  WorkItemsLogic  $instance
     * @return void
     * @throws ClientResponseException
     * @throws MissingParameterException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/8/27 21:09
     */
    public static function saveEs($id, $data, int $operationType, WorkItemsLogic $instance)
    {
        $instance->esLogic->save($id, $data, $operationType);
    }

    /**
     * 判断父需求是否有子任务，有的话转移到当前需求下
     * @param $cntId
     * @param $parentId
     * @return void
     * @throws ClientResponseException
     * @throws MissingParameterException
     * @throws ServerResponseException
     * @throws Throwable
     * <AUTHOR>
     * @date   2024/9/6 11:28
     */
    private function transferTasks($cntId, $parentId)
    {
        $childTaskList = $this->getChildren($parentId, WorkItemsModel::CNT_TYPE_TASK)->column('cnt_id');

        $childTaskList && $this->associateTasks(['cnt_id' => $cntId, 'cnt_id_list' => $childTaskList, 'cnt_type' => WorkItemsModel::CNT_TYPE_TASK]);
    }

    /**
     * 修改
     * @param $params
     * @return WorkItemsModel|array|mixed|Model
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function update($params)
    {
        validate(WorkItemsValidate::class)->scene('update')->check($params);

        $model = WorkItemsModel::findById($params['cnt_id']);
        if ( ! $model) {
            throw new NotFoundException();
        }

        if (isset($params['iteration_id'])) {
            if ( ! $this->isSetIteration($model)) {
                throw new ParamsException('不可设置迭代！');
            }
        }

        if (
            $model->isDemand() && isset($params['category_id'])
            && ! $params['category_id']
        ) {
            $params['category_id'] = '-1';
        }

        if (isset($params['title']) && trim($params['title']) == '') {
            throw new ParamsException("标题不可为空！");
        }


        if ($model->isDemand() && isset($params['parent_id'])) {
            if ($params['parent_id'] && $this->getChildren($params['parent_id'], WorkItemsModel::CNT_TYPE_TASK)->first()) {
                throw new ParamsException("不可选择已有任务的父需求！");
            }
        }


        $this->checkTaskParentIterationUnanimous($model, $params);


        if (isset($params['type_id'])) {
            $categorySettingsModel = ProjectCategorySettingsModel::findEnabledByCategoryId(
                $this->getSettingsTypeByCntType($model->cnt_type),
                $params['type_id']
            );
            if ( ! $categorySettingsModel) {
                throw new NotFoundException("类别不存在或已删除！");
            }
        }

        //根据状态更新相关信息
        if (isset($params['status_enum_id'])) {
            if ($model->isDemand() || $model->isFlaw()) {
                $commentType = $this->getSettingsTypeByCntType($model->cnt_type);
                $endStatusFormatKey = $this->getEndStatusFormatKey($commentType, $model->extends['project_id']);
                if ($this->isEnd($params, $model->extends['cnt_type'], $endStatusFormatKey)) {
                    $params['isEnd'] = true;
                } else {
                    $params['isEnd'] = false;
                }
            }

            if ($model->isTask()) {
                if ($this->isEnd($params, $model->extends['cnt_type'])) {
                    $params['finish_time'] = date('Y-m-d H:i:s');
                    $params['isEnd'] = true;
                } else {
                    $params['finish_time'] = null;
                    $params['isEnd'] = false;
                }
            }
        }


        Db::startTrans();
        try {
            $model->save($params);

            //设置预估工时
            $this->setTime($params);
            Db::commit();

            return $model->toDetail();
        } catch (Throwable $e) {
            Db::rollback();
            throw  $e;
        }
    }

    public function assembleSaveDiffString($project_id, array $params = []): string
    {
        $fieldConfig = FieldConfigModel::getColumnFieldNameFormat($project_id, FieldConfigModel::MODULE_TYPE_DEFECT);

        $demandsList = $iterationList = $userList = [];
        if ( ! empty($params['parent_id'])) {
            //关联需求
            $demands = $this->demandSelector(['project_id' => $project_id, 'scenario' => 3]);
            $demandsList = array_column($demands->toArray()['data'], 'label', 'value');

        }
        if ( ! empty($params['iteration_id'])) {
            //迭代
            $iterationList = IterationModel::status()->where('project_id', $project_id)->column('iteration_name', 'iteration_id');

        }

        //用户
        $userKeys = ["handler_uid", "developer_uid", "tester_uid", "cc_uid", "verify_uid", "closure_uid", "suspend_uid", "solve_uid"];
        $has = array_intersect($userKeys, array_keys($params));
        if ($has) {
            $users = (new ProjectUserLogic())->selectorListQuery(0, '');
            $userList = array_column($users, 'user_name', 'user_id');
        }

        $diffString = '<p>';
        foreach ($params as $key => $value) {
            if (empty($value['old']) && empty($value['new'])) {
                continue;
            }

            if ($key == 'enclosure') {
                //附件
                $diffString .= "附件： ";
                if (empty($value['old'])) {
                    $diffString .= " 新增 ";
                    foreach ($value['new'] as $vn) {
                        $diffString .= "<a href='{$vn['url']}' >{$vn['name']}</a> 、";
                    }
                    $diffString = trim($diffString, '、');
                } else {
                    $oldTmp = [];
                    $newTmp = [];
                    foreach (json_decode($value['old'], true) as $vo) {
                        $oldTmp[] = $vo['name']."|".$vo['url'];
                    }
                    foreach ($value['new'] as $vn) {
                        $newTmp[] = $vn['name']."|".$vn['url'];
                    }

                    $del = array_diff($oldTmp, $newTmp);
                    $add = array_diff($newTmp, $oldTmp);
                    $diffString .= $this->buildAttachmentChangeText($add, $del);
                }
                $diffString .= "<br/>";

            } elseif ( ! empty($fieldConfig[$key])) {
                $keyConfig = $fieldConfig[$key];
                $keyName = $keyConfig['field_label'];
                $oldValue = ! empty($value['old']) ? $value['old'] : '空';
                $newValue = ! empty($value['new']) ? $value['new'] : '空';
                if ($key == 'parent_id') {
                    $oldValue = $demandsList[$value['old']] ?? '空';
                    $newValue = $demandsList[$value['new']] ?? '空';
                }
                if ($key == 'iteration_id') {
                    $oldValue = $iterationList[$value['old']] ?? '空';
                    $newValue = $iterationList[$value['new']] ?? '空';
                }

                if (in_array($key, $userKeys)) {
                    if (is_array($value['old']) || is_array($value['new'])) {
                        if (is_string($value['old'])) {
                            json_decode($value['old']);
                            $isJson = json_last_error() === JSON_ERROR_NONE;
                            $value['old'] = $isJson ? (json_decode($value['old']) ?? []) : $value['old'];
                        }
                        if ( ! is_array($value['old'])) {
                            $value['old'] = [$value['old']];
                        }

                        $oldValue = ! empty($value['old']) ? array_intersect_key($userList, array_flip($value['old'])) : '空';
                        $newValue = ! empty($value['new']) ? array_intersect_key($userList, array_flip($value['new'])) : '空';

                        $oldValue = is_array($oldValue) ? implode('、', $oldValue) : $oldValue;
                        $newValue = is_array($newValue) ? implode('、', $newValue) : $newValue;
                    } else {
                        $oldValue = $userList[$value['old']] ?? '空';
                        $newValue = $userList[$value['new']] ?? '空';
                    }
                }
                if ($keyConfig['component_type'] == 'Select') {
                    $oldValue = str_replace(['["', '"]'], ' ', $oldValue);
                }

                $diffString .= $keyName.': '.$oldValue.' -> '.$newValue.'<br/>';
            }

        }

        return $diffString.'</p>';
    }


    /**
     * 构建附件变更（新增/删除）的文本描述
     */
    private function buildAttachmentChangeText(array $added, array $deleted): string
    {
        $result = "";
        if ( ! empty($added)) {
            $addedLinks = [];
            foreach ($added as $item) {
                [$name, $url] = explode("|", $item);
                $safeUrl = htmlspecialchars($url, ENT_QUOTES, 'UTF-8');
                $safeName = htmlspecialchars($name, ENT_QUOTES, 'UTF-8');
                $addedLinks[] = "<a href='{$safeUrl}'>{$safeName}</a>";
            }
            $result .= " 新增 ".implode("、", $addedLinks).";";
        }

        if ( ! empty($deleted)) {
            $deletedLinks = [];
            foreach ($deleted as $item) {
                [$name, $url] = explode("|", $item);
                $safeUrl = htmlspecialchars($url, ENT_QUOTES, 'UTF-8');
                $safeName = htmlspecialchars($name, ENT_QUOTES, 'UTF-8');
                $deletedLinks[] = "<a href='{$safeUrl}'>{$safeName}</a>";
            }
            $result .= " 删除 ".implode("、", $deletedLinks).";";
        }

        return $result;
    }


    /**
     * 获取所有后代节点id
     * @param  array  $data      二维数组集合
     * @param  int    $parentId  当前节点的 ID
     */
    public function getDescendants(
        array $data,
        $parentId,
        $parentIdName = 'parent_id',
        $idName = 'cnt_id'
    ) {
        $childList = [];
        foreach ($data as $item) {
            // 如果当前节点的 parent_id 等于传入的 parentId，说明是该节点的子节点
            if ($item[$parentIdName] === $parentId) {
                // 将此子节点添加到列表中
                $childList[] = $item[$idName];

                // 递归获取此子节点的后代节点
                $childList = array_merge(
                    $childList,
                    $this->getDescendants(
                        $data,
                        $item[$idName],
                        $parentIdName,
                        $idName
                    )
                );
            }
        }

        return $childList;
    }

    /**
     * 通过rootId获取树
     * @param  $rootId
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/9/11 16:14
     */
    public function getTree($rootId): array
    {

        $tree = $this->esLogic->esSearch([
            [
                'field_name'   => 'root_id',
                'value'        => $rootId,
                'type'         => 'term',
                'operate_type' => 'equal'
            ],
        ], $this->esLogic::NOT_PAGE_MAX, 1, null)->getCollection()->column(
            null,
            'cnt_id'
        );

        return $tree;
    }


    /**
     * 删除
     * @param $id
     * @return void
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws MissingParameterException
     * @throws Throwable
     * <AUTHOR>
     * @date   2024/9/10 11:56
     */
    public function delete($id)
    {
        $model = WorkItemsModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException();
        }

        if ($model->extends['is_delete'] === BaseModel::DELETE_YES) {
            return;
        }

        Db::startTrans();
        try {
            //获取树
            $tree = $this->getTree($model->extends['root_id']);

            //删除所有后代
            $this->recursiveChildren(
                $tree,
                $model->cnt_id,
                function (WorkItemsModel $model) {
                    //缺陷不删除,只是解除关联
                    if ($model->isFlaw()) {
                        $model->save(['parent_id' => 0]);
                    } else {
                        $model->save(['is_delete' => BaseModel::DELETE_YES]);
                    }
                }
            );

            $model->save(['is_delete' => BaseModel::DELETE_YES]);

            //更新父级的工时
            $model->isTask()
            && $this->updateWorkHours(
                $model->extends['parent_id']
            );

            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw  $e;
        }
    }

    /**
     * 设置预估工时
     * @param $params
     * @return void
     * @throws Throwable
     * <AUTHOR>
     * @date   2024/8/31 15:03
     */
    public function setTime($params): void
    {
        if ($params['estimated_work_hours'] ?? false) {
            (new WorkHoursLogic)->create([
                'cnt_id'        => $params['cnt_id'],
                'type'          => WorkHoursModel::TYPE_ESTIMATED,
                'working_hours' => $params['estimated_work_hours'],
            ], 1);
        }
    }


    /**
     * 获取子节点
     * @param $cntId
     * @param $cntType array|int|null 指定子节点类型
     * @return Collection|\think\model\Collection
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/9/26 15:35
     */
    public function getChildren($cntId, $cntType = null)
    {
        $where = [
            ['field_name' => 'parent_id', 'value' => $cntId, 'type' => "term", 'operate_type' => "equal"]
        ];
        if ($cntType) {
            if (is_array($cntType)) {
                $where[] = [
                    'field_name' => 'cnt_type',
                    'value'      => $cntType,
                    'type'       => "selector"
                ];
            } else {
                $where[] = [
                    'field_name'   => 'cnt_type',
                    'value'        => $cntType,
                    'type'         => "term",
                    'operate_type' => "equal"
                ];
            }
        }

        return $this->esLogic->esSearch($where)->getCollection();
    }

    /**
     * 递归修改所有后代
     * @param  array &$tree      二维数组集合
     * @param  int    $parentId  当前节点的 ID
     * @param         $data      array|callable 要修改的数据|闭包处理
     * @return void
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws MissingParameterException
     * <AUTHOR>
     * @date   2024/9/12 10:43
     */
    public function recursiveChildren(array &$tree, $parentId, $data)
    {
        foreach ($tree as &$item) {
            // 如果当前节点的 parent_id 等于传入的 parentId，说明是该节点的子节点
            if ($item['parent_id'] === $parentId) {
                $model = WorkItemsModel::findById($item['cnt_id']);
                if ( ! $model) {
                    break;
                }
                is_callable($data) ? $data($model) : $model->save($data);

                // 递归子节点
                $this->recursiveChildren($tree, $item['cnt_id'], $data);
            }
        }
    }

    /**
     * 详情
     * @param $id
     * @return array
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function detail($id)
    {

        $model = WorkItemsModel::findById($id);
        if ( ! $model) {
            throw new NotFoundException();
        }

        $details = $model->toDetail();

        $children = $this->getChildren($model->cnt_id);

        if ($details['project_id'] ?? false) {
            $details['project_name'] = ProjectModel::findById((int)$details['project_id'])['project_name'] ?? '';
        }

        return [
            'hasChildDemand' => $children->where(
                'cnt_type',
                '=',
                WorkItemsModel::CNT_TYPE_DEMAND
            )
                ->isEmpty() ? 0 : 1,
            'hasChildTask'   => $children->where(
                'cnt_type',
                '=',
                WorkItemsModel::CNT_TYPE_TASK
            )
                ->isEmpty() ? 0 : 1,
            'hasChildFlaw'   => $children->where(
                'cnt_type',
                '=',
                WorkItemsModel::CNT_TYPE_FLAW
            )
                ->isEmpty() ? 0 : 1,
            'data'           => $details,
            'field_list'     => $model->getFieldList(),
        ];
    }

    /**
     * 根据cnt_id获取详情
     * @param  int  $cntId
     * @return mixed|void
     * User Long
     * Date 2025/2/19
     */
    public static function getWorkItemsDetailByCntId(int $cntId)
    {
        $model = WorkItemsModel::findById($cntId);

        if ( ! $model) {
            return;
        }

        return $model->toDetail();
    }

    /**
     * 解析状态中的"所有结束状态","所有未结束状态"，只对多选有效
     * @param  array  $params  参数数组
     * @return void 包含status_enum_id的数组元素引用
     */
    public function resolveStatusEnumId(&$params)
    {
        $result = [];
        $findStatusEnumId = function (&$array) use (&$findStatusEnumId, &$result) {
            foreach ($array as &$item) {
                if (is_array($item)) {
                    //只对多选生效
                    if (
                        isset($item['field_name'])
                        && $item['field_name'] === 'status_enum_id'
                        && ($item['type'] ?? null) == 'selector'
                    ) {
                        $result[] = &$item;
                    }
                    $findStatusEnumId($item);
                }
            }
        };

        $findStatusEnumId($params);
        if ( ! $result) {
            return;
        }

        $parC = new Collection($params);
        $cntType = $parC->where('field_name', '=', 'cnt_type')->first()['value'] ?? null;
        if ( ! $cntType) {
            throw new Exception("缺少cnt_type");
        }
        if ($cntType == WorkItemsModel::CNT_TYPE_TASK) {
            $endStatusList = [3];
        } else {
            $endStatusList = $this->getEndStatusFormatKey(
                $this->getSettingsTypeByCntType($parC->where('field_name', '=', 'cnt_type')->first()['value'] ?? null),
                $parC->where('field_name', '=', 'project_id')->first()['value'] ?? null
            );
        }
        if ( ! $endStatusList) {
            return;
        }

        foreach ($result as &$statusItem) {
            $value = $statusItem['value'] ?? null;
            if ( ! $value) {
                continue;
            }

            $value = ! is_array($value) ? [$value] : $value;

            $searchParams = [
                'or' => [
                    $statusItem
                ]
            ];

            foreach ($value as $item) {
                if ($item == -1 || $item == -2) {
                    $var = ['or' => []];
                    foreach ($endStatusList as $endStatus) {
                        if ($cntType == WorkItemsModel::CNT_TYPE_TASK) {
                            $var['or'][] = ['field_name' => 'status_enum_id', 'value' => $endStatus, 'type' => "term", 'operate_type' => "equal"];
                        } else {
                            $var['or'][] = [
                                'and' => [
                                    ['field_name' => 'flow_status_id', 'value' => $endStatus['flow_status_id'], 'type' => "term", 'operate_type' => "equal"],
                                    ['field_name' => 'status_enum_id', 'value' => $endStatus['status_enum_id'], 'type' => "term", 'operate_type' => "equal"]
                                ]
                            ];
                        }
                    }
                    if ($item == -2) {
                        $var = ['not' => [$var]];
                    }

                    $searchParams['or'][] = $var;
                }
            }

            $statusItem = $searchParams;
        }
    }

    /**
     * 解析"空"值，只对多选有效
     * @param  array  $params  参数数组
     * @return void 包含status_enum_id的数组元素引用
     */
    public function resolveNull(&$params)
    {
        $result = [];
        $findStatusEnumId = function (&$array) use (&$findStatusEnumId, &$result) {
            foreach ($array as &$item) {
                if (is_array($item)) {
                    //只对多选生效
                    if (($item['type'] ?? null) == 'selector') {
                        $result[] = &$item;
                    }
                    $findStatusEnumId($item);
                }
            }
        };

        $findStatusEnumId($params);
        if ( ! $result) {
            return;
        }


        foreach ($result as &$statusItem) {
            $value = $statusItem['value'] ?? null;
            if ( ! $value) {
                continue;
            }

            $value = ! is_array($value) ? [$value] : $value;

            $searchParams = [
                'or' => [
                    $statusItem
                ]
            ];

            foreach ($value as $k => $item) {
                if ($item == 'null') {
                    $searchParams['or'][] = ['field_name' => $statusItem['field_name'], 'exists' => 0];
                    unset($value[$k]); //去掉"null",因为有的类型不支持字符串搜索
                }
            }
            $searchParams['or'][0]['value'] = $value;
            $statusItem = $searchParams;
        }
    }


    /**
     * 分页查询
     * @param $params
     * @return array|null[]|Paginator
     * @throws DbException
     * <AUTHOR>
     * @date   2024/7/8 上午10:18
     */
    public function pageQuery($params)
    {
        validate(WorkItemsValidate::class)->scene('pageQuery')->check($params);

        $params['searchParams'][] = [
            'field_name'   => 'project_id',
            'value'        => $params['project_id'],
            'type'         => "term",
            'operate_type' => "equal"
        ];
        foreach ($params['searchParams'] as $k => $searchParam) {
            if (($searchParam['field_name'] ?? null) === 'category_id') {
                if (isset($searchParam['value']) && $searchParam['value'] == 0) {
                    unset($params['searchParams'][$k]);
                }
            }
        }

        if ($params['test_case_id'] ?? false) {
            $params['searchParams'][] = [
                'field_name' => 'cnt_id',
                'value'      => TestCaseWorkLogic::getIdListByType($params['test_case_id'], TestCaseWorkLogic::TYPE_TEST_CASE_ID),
                'type'       => "selector",
            ];
        }

        $order = $this->formatOrderParams($params['order'] ?? []);
        $statistics = null;

        $isTreeOutput = ! ! ($params['is_tree'] ?? false);
        // 当明确要求非树形输出时，不应显示父级或子级，因为它们仅与树形结构相关。
        // 当要求树形输出时，才考虑 $showParents 和 $showChildren 的原始意图。
        // 在 pageQuery 的上下文中，$showChildren 通常为 false。
        $actualShowParents = $isTreeOutput ? ! ! ($params['is_tree'] ?? false) : false; //  $showParents 应该由 $isTreeOutput 控制
        $actualShowChildren = $isTreeOutput ? false : false;                          // 在此上下文中，$showChildren 通常为 false

        $resultData = $this->esLogic->esSearchSummaryForTree(
            ['public_field' => $params['searchParams']],
            $isTreeOutput,          // 新的 isTreeOutput 参数
            $actualShowParents,     // 根据 isTreeOutput 调整
            $actualShowChildren,    // 根据 isTreeOutput 调整
            $params['field_list'],
            $statistics,            // 传递引用，esSearchSummaryForTree 会填充它
            $order,                 // $mainDataSort
            null,                   // $mainDataGroupBy 总是 null for pageQuery
            [],                     // $childrenOrder
            $this->listHandler()
        );

        $resultItems = $resultData; // $resultData 是扁平列表或树根节点列表

        $total = count($resultItems);

        $pageSize = getPageSize();
        $paginatedItems = array_slice($resultItems, ($pageSize['page'] - 1) * $pageSize['list_rows'], $pageSize['list_rows']);

        // 返回结构与 pageQuerySummary 的非分组情况对齐，包含 statistics
        return array_merge(
            Paginator::make($paginatedItems, $pageSize['list_rows'], $pageSize['page'], $total)->toArray(),
            ['statistics' => $statistics]
        );
    }


    /**
     * 接收多组查询参数
     * @param $params
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date   2024/11/7 17:56
     */
    public function pageQuerySummary($params)
    {
        validate(WorkItemsValidate::class)->scene('pageQuery')->check($params);

        $params['searchParams']['public_field'][] = [
            'field_name'   => 'project_id',
            'value'        => $params['project_id'],
            'type'         => "term",
            'operate_type' => "equal"
        ];

        if ($params['iteration_id']) {
            $params['searchParams']['public_field'][] = [
                'field_name'   => 'iteration_id',
                'value'        => $params['iteration_id'],
                'type'         => "term",
                'operate_type' => "equal"
            ];
        } else {
            $params['searchParams']['public_field'][] = [
                'field_name' => 'iteration_id',
                'exists'     => 0
            ];
        }

        $showParents = false;
        if (
            ($params['is_tree'] ?? false)
            && in_array(WorkItemsModel::CNT_TYPE_DEMAND, $params['cnt_type_list'])
        ) {
            $showParents = true;
        }

        $statistics = null;
        $order = $this->formatOrderParams($params['order'] ?? []);

        $resultData = [];

        $itemProcessor = $this->listHandler();
        $groupByField = $params['group_by_field'] ?? null;
        $isTreeOutputForSummary = ! ! ($params['is_tree'] ?? false);

        // 确定 showParents 和 showChildren 的实际值
        // 只有当明确要求树形输出时，这些参数才有意义。
        $actualShowParents = $isTreeOutputForSummary ? $showParents : false;
        // 在 pageQuerySummary 上下文中，即使是树形，通常也不立即显示所有子项，除非特别指定
        $actualShowChildren = $isTreeOutputForSummary ? ($params['show_children_in_summary_tree'] ?? false) : false;


        $processedResult = $this->esLogic->esSearchSummaryForTree(
            $params['searchParams'],
            $isTreeOutputForSummary,    // 控制 esSearchSummaryForTree 的输出结构
            $actualShowParents,
            $actualShowChildren,
            $params['field_list'],
            $statistics,
            $order,                     // $mainDataSort
            $groupByField,              // $mainDataGroupBy
            [],                         // $childrenOrder
            $itemProcessor
        );

        // 如果是分组结果，esSearchSummaryForTree(isTreeOutput: false, mainDataGroupBy: 'field') 会返回特定结构
        if ($groupByField && isset($processedResult['_is_grouped_result'])) {
            if (empty($params['order'])) {
                foreach ($processedResult['groups'] as &$group) {
                    usort($group['children'], function ($a, $b) {
                        $typeA = $a['cnt_type'] ?? PHP_INT_MAX;
                        $typeB = $b['cnt_type'] ?? PHP_INT_MAX;
                        return $typeA - $typeB;
                    });
                }
            }
            return array_merge(
                Paginator::make($processedResult['groups'], getPageSize()['list_rows'], getPageSize()['page'], count($processedResult['groups']))->toArray(),
                [
                    'statistics'         => $statistics,
                    '_is_grouped_result' => true,
                ]
            );
        }

        // 如果不是分组，或者 $isTreeOutputForSummary 为 true (结果已经是树或扁平列表)
        $resultData = $processedResult;

        // 如果请求的是树形结构 ($isTreeOutputForSummary is true)，则 $resultData 已经是树，不需要 buildTree。
        // 如果请求的是扁平列表 ($isTreeOutputForSummary is false)，则 $resultData 已经是扁平列表。
        // 原有的 buildTree 和 flawItems 合并逻辑，现在由 esSearchSummaryForTree(isTreeOutput: true) 内部处理，
        // 或者在 $isTreeOutputForSummary 为 false 时，调用者得到的是扁平列表，不需要此特殊合并。

//        if (empty($params['order']) && ! $isTreeOutputForSummary) { // 仅当输出是扁平列表且未指定排序时，才应用默认排序
        if (empty($params['order'])) {
            usort($resultData, function ($a, $b) {
                $typeA = $a['cnt_type'] ?? PHP_INT_MAX;
                $typeB = $b['cnt_type'] ?? PHP_INT_MAX;
                return $typeA - $typeB;
            });
        }

        $total = count($resultData);
        $pageSize = getPageSize();
        $paginatedResult = array_slice($resultData, ($pageSize['page'] - 1) * $pageSize['list_rows'], $pageSize['list_rows']);

        return array_merge(Paginator::make($paginatedResult, $pageSize['list_rows'], $pageSize['page'], $total)->toArray(), [
            'statistics' => $statistics,
        ]);
    }


    /**
     * 接收多组查询参数
     * @param $params
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date   2024/11/7 17:56
     */
    public function pageQueryMyJob($params)
    {
        $groupByField = $params['group_by_field'] ?? null;

        switch ($params['type']) {
        case 1: //我的待办
            $params['searchParams']['public_field'][] = ['field_name' => 'handler_uid', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"];
            $params['searchParams']['public_field'][] = ['field_name' => 'isEnd', 'value' => false, 'type' => "term", 'operate_type' => "equal"];
            break;
        case 2: //我的已办
            $params['searchParams']['public_field'][] = ['field_name' => 'handler_uid', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"];
            $params['searchParams']['public_field'][] = ['field_name' => 'isEnd', 'value' => true, 'type' => "term", 'operate_type' => "equal"];
            break;
        case 3: //我创建的
            $params['searchParams']['public_field'][] = ['field_name' => 'create_by', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"];
            break;

        case 4: //我参与的
            $params['searchParams']['public_field'][] = [
                "or" => [
                    ['field_name' => 'handler_uid', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
                    ['field_name' => 'create_by', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
                    ['field_name' => 'update_by', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
                    ['field_name' => 'tester_uid', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
                    ['field_name' => 'developer_uid', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
                    ['field_name' => 'verify_uid', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
                    ['field_name' => 'closure_uid', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
                    ['field_name' => 'suspend_uid', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
                    ['field_name' => 'solve_uid', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
                ]
            ];
            break;
        case 5: //指定用户参与的
//            if (empty($params['user_ids']) || ! is_array($params['user_ids'])) {
//                throw new ParamsException("user_ids参数不能为空且必须为数组");
//            }
            $params['searchParams']['public_field'][] = [
                "or" => [
                    ['field_name' => 'handler_uid', 'value' => $params['user_ids'], 'type' => "selector"],
                    ['field_name' => 'create_by', 'value' => $params['user_ids'], 'type' => "selector"],
                    ['field_name' => 'update_by', 'value' => $params['user_ids'], 'type' => "selector"],
                    ['field_name' => 'tester_uid', 'value' => $params['user_ids'], 'type' => "selector"],
                    ['field_name' => 'developer_uid', 'value' => $params['user_ids'], 'type' => "selector"],
                    ['field_name' => 'verify_uid', 'value' => $params['user_ids'], 'type' => "selector"],
                    ['field_name' => 'closure_uid', 'value' => $params['user_ids'], 'type' => "selector"],
                    ['field_name' => 'suspend_uid', 'value' => $params['user_ids'], 'type' => "selector"],
                    ['field_name' => 'solve_uid', 'value' => $params['user_ids'], 'type' => "selector"],
                ]
            ];
            if ( ! empty($params['cnt_type_list']) && is_array($params['cnt_type_list'])) {
                $params['searchParams']['public_field'][] = [
                    'field_name' => 'cnt_type',
                    'value'      => $params['cnt_type_list'],
                    'type'       => 'selector'
                ];
            }
            if ( ! empty($params['project_id']) && is_array($params['project_id'])) {
                $params['searchParams']['public_field'][] = [
                    'field_name' => 'project_id',
                    'value'      => $params['project_id'],
                    'type'       => 'selector'
                ];
            }
            break;
        default:
            throw new ParamsException();
        }

        $pageSize = getPageSize();
        $statistics = null;

        $projectId = $params['project_id'] ?? null;
        if ( ! $projectId) {
            foreach ($params['searchParams']['public_field'] ?? [] as $v) {
                if ($v['field_name'] == 'project_id') {
                    $projectId = $v['value'];
                }
            }
        }


        $esResult = $this->esLogic->esSearchSummary(
            $params['searchParams'],
            $pageSize['list_rows'],
            $pageSize['page'],
            $params['field_list'],
            $statistics, // $statistics 是通过引用传递的
            $groupByField,
            $projectId
        );

        if ($groupByField && isset($esResult['_is_grouped_result']) && $esResult['_is_grouped_result'] === true) {
            $processedGroups = [];
            $totalItemsInGroups = 0;
            $missingKye = null;
            $transferUtil = null;
            if ($groupByField) {
                $moduleId = [
                    FieldConfigModel::MODULE_TYPE_REQUIREMENT,
                    FieldConfigModel::MODULE_TYPE_TASK,
                    FieldConfigModel::MODULE_TYPE_DEFECT,
                ];
                $transferUtil = new Transfer($moduleId, $projectId);
            }

            if (isset($esResult['groups']) && is_array($esResult['groups'])) {
                foreach ($esResult['groups'] as $key => $group) {
                    $originalGroupKey = $group['group_key'];
                    $displayGroupKey = $originalGroupKey;
                    $groupLabel = $originalGroupKey;

                    if ($originalGroupKey == '-1') { // ES missing bucket key
                        $missingKye = $key;
                        $displayGroupKey = '--'; // 用于显示的 group_key
                        $groupLabel = '--';      // group_label 也为 --
                    } elseif ($transferUtil && $groupByField) {
                        try {
                            $parsedLabel = $transferUtil->parse($groupByField, $originalGroupKey);
                            if (is_array($parsedLabel) && isset($parsedLabel['label'])) {
                                $groupLabel = $parsedLabel['label'];
                            } elseif (is_string($parsedLabel) || is_numeric($parsedLabel)) {
                                $groupLabel = (string)$parsedLabel;
                            }
                        } catch (Throwable $e) {
                            // 解析失败，groupLabel 保持为原始 groupKey
                        }
                    }

                    $processedItems = (new Collection($group['items']))
                        ->each($this->listHandler()) // 将 listHandler 应用于组内的项目
                        ->toArray();
                    $processedGroups[] = [
                        'group_key'   => $displayGroupKey, // 显示用的key，可能是 '--'
                        'group_label' => $groupLabel,      // 解析后的标签或原始键
                        'children'    => $processedItems,
                        'count'       => $group['count']
                    ];
                    $totalItemsInGroups += $group['count'];
                }
            }
            //将--分组放最后
            if ($missingKye !== null) {
                $temp = $processedGroups[$missingKye];
                unset($processedGroups[$missingKye]);
                $processedGroups[] = $temp;
                $processedGroups = array_values($processedGroups);
            }
            return array_merge(
                Paginator::make([], $pageSize['list_rows'], $pageSize['page'], $totalItemsInGroups)->toArray(),
                [
                    'data'       => $processedGroups,
                    'statistics' => $statistics,
                    'is_grouped' => true,
                    'total'      => count($processedGroups)
                ]
            );

        } else {
            // 非分组情况，$esResult 是一个 Paginator 实例
            $itemList = $esResult->each($this->listHandler());
            return array_merge($itemList->toArray(), ['statistics' => $statistics]);
        }
    }


    /**
     * 获取我的工作项各场景数量统计
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     */
    public function getMyJobCount($projectIds)
    {
        $result = [
            'todo'     => 0,     // 我的待办
            'done'     => 0,     // 我的已办
            'created'  => 0,  // 我创建的
            'involved' => 0  // 我参与的
        ];

        // 我的待办
        $result['todo'] = $this->esLogic->esSearch([

            ['field_name' => 'handler_uid', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
            ['field_name' => 'isEnd', 'value' => false, 'type' => "term", 'operate_type' => "equal"],
            ['field_name' => 'project_id', 'value' => $projectIds, 'type' => "selector"],
        ], 1)->total();

        // 我的已办
        $result['done'] = $this->esLogic->esSearch([
            ['field_name' => 'handler_uid', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
            ['field_name' => 'isEnd', 'value' => true, 'type' => "term", 'operate_type' => "equal"],
            ['field_name' => 'project_id', 'value' => $projectIds, 'type' => "selector"],

        ], 1)->total();

        // 我创建的
        $result['created'] = $this->esLogic->esSearch([
            ['field_name' => 'create_by', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
            ['field_name' => 'project_id', 'value' => $projectIds, 'type' => "selector"],

        ], 1)->total();

        // 我参与的
        $result['involved'] = $this->esLogic->esSearch([
            ['field_name' => 'project_id', 'value' => $projectIds, 'type' => "selector"],
            [
                "or" => [
                    ['field_name' => 'handler_uid', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
                    ['field_name' => 'create_by', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
                    ['field_name' => 'update_by', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
                    ['field_name' => 'tester_uid', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
                    ['field_name' => 'developer_uid', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
                    ['field_name' => 'verify_uid', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
                    ['field_name' => 'closure_uid', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
                    ['field_name' => 'suspend_uid', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"],
                    ['field_name' => 'solve_uid', 'value' => Ctx::$userId, 'type' => "term", 'operate_type' => "equal"]
                ]
            ]
        ], 1)->total();

        return $result;
    }


    /**
     * 接收多组查询参数按用户分组
     * @param $params
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date   2024/11/7 17:56
     */
    public function pageQuerySummaryGroupUser($params)
    {
        validate(WorkItemsValidate::class)->scene('pageQuery')->check($params);

        $params['searchParams']['public_field'][] = [
            'field_name'   => 'project_id',
            'value'        => $params['project_id'],
            'type'         => "term",
            'operate_type' => "equal"
        ];
        if ($params['iteration_id']) {
            $params['searchParams']['public_field'][] = [
                'field_name'   => 'iteration_id',
                'value'        => $params['iteration_id'],
                'type'         => "term",
                'operate_type' => "equal"
            ];
        } else {
            $params['searchParams']['public_field'][] = [
                'field_name' => 'iteration_id',
                'exists'     => 0
            ];
        }

        $esResult = $this->esLogic->esSearchSummaryGroupUser(
            $params['searchParams'],
            $params['field_list']
        );
        if ( ! $esResult['data']) {
            return [];
        }
        $userList = ProjectUserModel::selectListById(array_keys($esResult['statistics']), $params['project_id'])
            ->push([
                'user_id'   => -1,
                'user_name' => '--',
            ])
            ->each(function ($item) {
                $item['node_pid'] = 0;
                $item['node_id'] = 'user-'.$item['user_id'];

                return $item;
            })
            ->toArray();
        $data = [];


        $userIds = array_column($userList, 'user_id');

        (new Collection($esResult['data']))->each($this->listHandler())
            ->each(function ($item) use ($userIds) { //将不存在于项目中的处理人改为-1
                if ( ! empty($item['handler_uid'])) {
                    $handlerUids = is_array($item['handler_uid']) ? $item['handler_uid'] : [$item['handler_uid']];
                    $item['handler_uid'] = array_map(function ($uid) use ($userIds) {
                        return in_array($uid, $userIds) ? $uid : -1;
                    }, $handlerUids);
                }
                return $item;
            })->each(function ($item) use (&$data) {
                $item['cnt_type'] = (int)$item['cnt_type'];
                $handlerUid = ! empty($item['handler_uid'])
                    ? $item['handler_uid'] : [-1];
                $handlerUid = is_array($handlerUid) ? $handlerUid
                    : [$handlerUid];
                foreach ($handlerUid as $v) {
                    $item['node_pid'] = 'user-'.$v;
                    $item['node_id'] = 'cnt-'.$item['cnt_id'].'-'.$v;
                    $data[] = $item;
                }
            });


        $tree = $this->buildTree(
            array_merge($data, $userList),
            'node_id',
            'node_pid'
        );

        foreach ($tree as &$v) {
            $cntTypeCount
                = array_column(
                $esResult['statistics'][$v['user_id']]['cnt_type_count']['buckets']
                ?? [],
                null,
                'key'
            );
            $workHourSum
                = $esResult['statistics'][$v['user_id']]['work_hour_sum'] ?? [];
            $v['statistics'] = [
                'demand_count'         => $cntTypeCount[WorkItemsModel::CNT_TYPE_DEMAND]['doc_count']
                    ?? 0,
                'task_count'           => $cntTypeCount[WorkItemsModel::CNT_TYPE_TASK]['doc_count']
                    ?? 0,
                'flaw_count'           => $cntTypeCount[WorkItemsModel::CNT_TYPE_FLAW]['doc_count']
                    ?? 0,
                'demand_end_count'     => $cntTypeCount[WorkItemsModel::CNT_TYPE_DEMAND]['not_end']['doc_count']
                    ?? 0,
                'task_end_count'       => $cntTypeCount[WorkItemsModel::CNT_TYPE_TASK]['not_end']['doc_count']
                    ?? 0,
                'flaw_end_count'       => $cntTypeCount[WorkItemsModel::CNT_TYPE_FLAW]['not_end']['doc_count']
                    ?? 0,
                'estimated_work_hours' => ($workHourSum['estimated_work_hours']['value'] ?? 0) ? (float)number_format($workHourSum['estimated_work_hours']['value'], 2) : 0,
                'actual_work_hours'    => ($workHourSum['actual_work_hours']['value'] ?? 0) ? (float)number_format($workHourSum['actual_work_hours']['value'], 2) : 0,
                'remaining_work'       => ($workHourSum['remaining_work']['value'] ?? 0) ? (float)number_format($workHourSum['remaining_work']['value'], 2) : 0,
            ];
        }
        //无处理人分组数据为空删除
        if ( ! $tree[count($tree) - 1]['children']) {
            unset($tree[count($tree) - 1]);
        }

        return $tree;
    }


    /**
     * 集合数据处理
     * @param $dataList
     * @return Closure
     * <AUTHOR>
     * @date   2024/11/14 15:02
     */
    public function listHandler(): callable
    {
        return function ($item) { // Item is passed by value, modifications return a new array or null
            if ( ! is_array($item) || empty($item['cnt_id'])) {
                return null;
            }

            $item['is_set_iteration'] = $this->isSetIteration($item);

            // Integrate type casting for cnt_type
            if (isset($item['cnt_type'])) {
                $item['cnt_type'] = (int)$item['cnt_type'];
            }
            $item['project_id'] = (int)$item['project_id'];
            return $item;
        };
    }

    /**
     * 是否可设置迭代
     * @param $item
     * @return bool
     * <AUTHOR>
     * @date   2024/11/14 14:50
     */
    public function isSetIteration($item)
    {
        return match ((int)$item['cnt_type']) { //是否可设置迭代
            WorkItemsModel::CNT_TYPE_DEMAND => ($item['subset_cnt_type'] ?? 0)
                != WorkItemsModel::CNT_TYPE_DEMAND,
            //含父级||含节点都不可设置迭代
            WorkItemsModel::CNT_TYPE_TASK => ! (($item['parent_id'] ?? null) || ($item['iteration_process_node_id'] ?? null)),
            WorkItemsModel::CNT_TYPE_FLAW => true,
        };
    }


    public function getEndStatusFormatKey(
        $flowStatusType
        = [
            BaseModel::SETTING_TYPE_DEMAND, //需求
            //        BaseModel::SETTING_TYPE_TASK,//任务
            BaseModel::SETTING_TYPE_DEFECT, //缺陷
        ],
        $projectId = null
    ) {
        $endStatusList = [];
        foreach (FlowStatusLogic::getEndStatus($flowStatusType, $projectId) as $v) {
            $endStatusList["{$v['flow_status_id']}-{$v['status_enum_id']}"] = $v;
        }

        return $endStatusList;
    }

    public function isEnd($data, $cntType, $demandEndStatusList = [])
    {
        $flowStatusId = $data['flow_status_id'] ?? 0;
        $statusEnumId = $data['status_enum_id'] ?? 0;
        if (($cntType == WorkItemsModel::CNT_TYPE_DEMAND) || $cntType == WorkItemsModel::CNT_TYPE_FLAW) {
            if (isset($demandEndStatusList["{$flowStatusId}-{$statusEnumId}"])) {
                return true;
            }
        } else {
            if (isset($statusEnumId) && $statusEnumId == EnumLogic::findEnumValue('completed')) {
                return true;
            }
        }

        return false;
    }


    /**
     * 格式化为树
     * @param $data
     * @param $parentId
     * @return array
     * <AUTHOR>
     * @date   2024/8/28 11:59
     */
    public function buildTree($data, $id = 'cnt_id', $pid = 'parent_id')
    {
        $tree = [];
        $itemsById = [];

        // 先将数据按 id 重新索引
        foreach ($data as $item) {
            $itemsById[$item[$id]] = $item;
            $itemsById[$item[$id]]['children'] = []; // 初始化子节点
        }

        // 遍历所有项，构建树
        foreach ($itemsById as &$item) {
            // 如果 parent_id 为 null 或者不存在于数据中，则是顶级节点
            if ($item[$pid] === 0 || ! isset($itemsById[$item[$pid]])) {
                $tree[] = &$item;
            } else {
                // 如果有父节点，将当前节点作为父节点的子节点
                $itemsById[$item[$pid]]['children'][] = &$item;
            }
        }

        return $tree;
    }

    /**
     * 更新工时，递归更新父节点工时
     * @param $cntId
     * @param $data  array 传值了表示要更新自己，然后递归父级，不传值代表要更新的值从子级取，所以从自己开始递归
     * @return void
     * @throws ClientResponseException
     * @throws MissingParameterException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/8/29 11:49
     */
    public function updateWorkHours($cntId, $data = [])
    {
        if ( ! $cntId) {
            return;
        }
        $model = WorkItemsModel::findById($cntId);
        if ( ! $model) {
            throw new NotFoundException();
        }
        $tree = $this->getTree($model->extends['root_id']);

        if ($data) {
            WorkItemsModel::findById($cntId)->save($data);
            //es批量插入只会在最后执行一次，所以这里tree会不包含自己
            if ( ! isset($tree[$cntId])) {
                $tree[$cntId] = $model->extends;
            }
            $tree[$cntId] = array_merge($tree[$cntId], $data);
        }

        //缺陷不需要更新树
        if ($model->isFlaw()) {
            return;
        }
        $this->recursiveParentHours(
            $tree,
            $data ? $model->extends['parent_id'] : $model->cnt_id
        );
    }

    /**
     * 递归更新父级节点的工时
     *
     * @param  array &   $data      二维数组集合
     * @param  int|null  $parentId  父级节点的 ID
     */
    private function recursiveParentHours(array &$data, $parentId)
    {
        if ($parentId === 0) {
            return;
        }

        // 同级别节点
        $sameLevelList = [];
        foreach ($data as $item) {
            if ($item['parent_id'] === $parentId) {
                $sameLevelList[] = $item;
            }
        }

        // 更新父级节点的值
        foreach ($data as &$item) {
            if ($item['cnt_id'] === $parentId) {
                $totalWork = $this->totalWork($sameLevelList);
                WorkItemsModel::findById($item['cnt_id'])->save($totalWork);
                //                更新已查出的值
                $item = array_merge($item, $totalWork);

                // 继续递归更新父级的父级
                if ($item['parent_id'] !== 0) {
                    $this->recursiveParentHours($data, $item['parent_id']);
                }
                break;
            }
        }
    }

    /**
     * 工作项集合工时合计
     * @param $dataList
     * @return array
     * <AUTHOR>
     * @date   2024/8/29 11:51
     */
    private function totalWork($dataList)
    {

        //剔除缺陷
        $dataList = array_filter($dataList, fn($v) => $v['cnt_type'] != WorkItemsModel::CNT_TYPE_FLAW);


        //进度取平均值，其他都是合计
        return [
            'estimated_work_hours'    => (float)number_format(array_sum(array_column($dataList, 'estimated_work_hours')), 2),
            'actual_work_hours'       => (float)number_format(array_sum(array_column($dataList, 'actual_work_hours')), 2),
            'remaining_work'          => (float)number_format(array_sum(array_column($dataList, 'remaining_work')), 2),
            'exceeding_working_hours' => (float)number_format(array_sum(array_column($dataList, 'exceeding_working_hours')), 2),
            'speed_of_progress'       => count($dataList) == 0
                ? 0
                : ((int)bcdiv((string)array_sum(array_column($dataList, 'speed_of_progress')), (string)count($dataList))),
            //            'speed_of_progress' => count($dataList) == 0 ? 0 : ((int)(array_sum(array_column($dataList, 'speed_of_progress')) / count($dataList))),
        ];
    }

    /**
     * 根据工作项id查询工作信息
     * @param  int  $workItemsId
     * @return WorkItemsModel|array|mixed|Model
     * User Long
     * Date 2024/8/28
     */
    public static function findWorkItemsInfoById(int $workItemsId)
    {
        $model = WorkItemsModel::findById($workItemsId);
        if ( ! $model) {
            throw new NotFoundException('当前内容已删除，请刷新页面');
        }

        return $model;
    }

    /**
     * 获取在指定状态的工作项id集合
     * @param $flowStatusId
     * @param $statusId
     * @param $projectCategorySettingsType
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * User Long
     * Date 2024/10/8
     */
    public static function getItemByStatusId(
        $flowStatusId,
        $statusId,
        $projectCategorySettingsType
    ) {
        // 处理es类型枚举与类别枚举不一致问题
        $cnt_type = self::handleProjectCategorySettingsType(
            $projectCategorySettingsType
        );

        return Es::getInstance()
            ->setQueryParam([
                'field_name'   => 'cnt_type',
                'value'        => $cnt_type,
                'type'         => 'term',
                'operate_type' => 'equal'
            ])
            ->setQueryParam([
                'field_name'   => 'flow_status_id',
                'value'        => $flowStatusId,
                'type'         => 'term',
                'operate_type' => 'equal'
            ])
            ->setQueryParam([
                'field_name'   => 'status_enum_id',
                'value'        => $statusId,
                'type'         => 'term',
                'operate_type' => 'equal'
            ])
            ->setPage(WorkItemsEsLogic::NOT_PAGE_MAX)
            ->getPaginator()
            ->getCollection()
            ->column('cnt_id');
    }

    /**
     * 批量设置工作项状态
     * @param  array  $cntIds    工作项id结合
     * @param  int    $statusId  目标状态id
     * @return void
     * @throws ClientResponseException
     * @throws MissingParameterException
     * @throws ServerResponseException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date   2024/8/29 16:35
     */
    public function setItemsStatusId(array $cntIds, $statusId)
    {
        Db::startTrans();
        try {
            WorkItemsModel::where(
                ['cnt_id' => $cntIds, 'is_delete' => BaseModel::DELETE_NOT]
            )
                ->select()->each(
                    function (WorkItemsModel $model) use ($statusId) {
                        $params = ['status_enum_id' => $statusId];
                        $model['status_enum_id'] = $statusId;
                        if ($this->isEnd($model, $model->extends['cnt_type'])) {
                            $params['finish_time'] = date('Y-m-d H:i:s');
                            $params['isEnd'] = true;
                        } else {
                            $params['finish_time'] = null;
                            $params['isEnd'] = false;
                        }
                        $model->save($params);
                    }
                );
            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw  $e;
        }
    }

    /**
     * 获取在指定分类下的工作项id集合
     * @param $categoryId
     * @param $projectCategorySettingsType
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * User Long
     * Date 2024/10/8
     */
    public static function getItemByCategoryId(
        $categoryId,
        $projectCategorySettingsType
    ) {
        // 处理es类型枚举与类别枚举不一致问题
        $cnt_type = self::handleProjectCategorySettingsType(
            $projectCategorySettingsType
        );

        return Es::getInstance()
            ->setQueryParam([
                'field_name'   => 'cnt_type',
                'value'        => $cnt_type,
                'type'         => 'term',
                'operate_type' => 'equal'
            ])
            ->setQueryParam([
                'field_name'   => 'category_id',
                'value'        => $categoryId,
                'type'         => 'term',
                'operate_type' => 'equal'
            ])
            ->setPage(WorkItemsEsLogic::NOT_PAGE_MAX)
            ->getPaginator()
            ->getCollection()
            ->column('cnt_id');
    }

    /**
     * 获取在指定类别下的工作项id集合
     * @param $projectCategorySettingsType
     * @param $categorySettingId
     * @return array
     * User Long
     * Date 2024/9/4
     * @throws ClientResponseException
     * @throws ServerResponseException
     */
    public static function getItemByCategorySettingId(
        $projectCategorySettingsType,
        $categorySettingId
    ) {
        // 处理es类型枚举与类别枚举不一致问题
        $cnt_type = self::handleProjectCategorySettingsType(
            $projectCategorySettingsType
        );

        return Es::getInstance()
            ->setQueryParam([
                'field_name'   => 'cnt_type',
                'value'        => $cnt_type,
                'type'         => 'term',
                'operate_type' => 'equal'
            ])
            ->setQueryParam([
                'field_name'   => 'type_id',
                'value'        => $categorySettingId,
                'type'         => 'term',
                'operate_type' => 'equal'
            ])
            ->setPage(WorkItemsEsLogic::NOT_PAGE_MAX)
            ->getPaginator()
            ->getCollection()
            ->column('cnt_id');
    }

    /**
     * 获取分类，需求数 es聚合数据
     * @param $projectId
     * @param $projectCategorySettingsType
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * User Long
     * Date 2024/10/8
     */
    public static function getAggsCategoryByProjectId(
        $projectId,
        $projectCategorySettingsType
    ) {
        // 处理es类型枚举与类别枚举不一致问题
        $cnt_type = self::handleProjectCategorySettingsType(
            $projectCategorySettingsType
        );

        return Es::getInstance()
            ->setQueryParam([
                'field_name'   => 'cnt_type',
                'value'        => $cnt_type,
                'type'         => 'term',
                'operate_type' => 'equal'
            ])
            ->setQueryParam([
                'field_name'   => 'project_id',
                'value'        => $projectId,
                'type'         => 'term',
                'operate_type' => 'equal'
            ])
            ->setAggsFidld('category_id')
            ->setLimit(WorkItemsEsLogic::NOT_PAGE_MAX)
            ->getAggData();
    }

    /**
     * 批量设置工作项分类
     * @param  array  $cntIds
     * @param  int    $categoryId
     * @return void
     * @throws ClientResponseException
     * @throws MissingParameterException
     * @throws ServerResponseException
     * @throws Throwable
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date   2024/8/30 17:08
     */
    public function setItemsCategoryId(array $cntIds, int $categoryId)
    {
        Db::startTrans();
        try {
            WorkItemsModel::where(
                ['cnt_id' => $cntIds, 'is_delete' => BaseModel::DELETE_NOT]
            )
                ->select()->each(
                    function (WorkItemsModel $model) use ($categoryId) {
                        $model->save(['category_id' => $categoryId]);
                    }
                );

            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw  $e;
        }
    }

    /**
     * 批量设置工作项类别
     * @param  array  $cntIds             工作项id合集
     * @param  int    $categorySettingId  类别id
     * @param  array  $flowStatus         工作流数据
     *                                    $flowStatus['flow_status_id'] = 工作流id;
     *                                    $flowStatus['status_enum_id'] = 开始状态id;
     * @throws ClientResponseException
     * @throws MissingParameterException
     * @throws ServerResponseException
     * @throws Throwable
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     *                                    User Long
     *                                    Date 2024/9/4
     */
    public function setItemsCategorySettingId(
        array $cntIds,
        int $categorySettingId,
        array $flowStatus = []
    ) {
        try {
            Db::startTrans();

            $data['type_id'] = $categorySettingId;
            if ($flowStatus) {
                $data['flow_status_id'] = $flowStatus['flow_status_id'];
                $data['status_enum_id'] = $flowStatus['status_enum_id'];
            }

            WorkItemsModel::where(
                ['cnt_id' => $cntIds, 'is_delete' => BaseModel::DELETE_NOT]
            )
                ->select()->each(function (WorkItemsModel $model) use ($data) {
                    $model->save($data);
                });

            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw  $e;
        }
    }

    /**
     * 状态流转（需求|缺陷）
     * @param $params
     * @return void
     * @throws ClientResponseException
     * @throws MissingParameterException
     * @throws ServerResponseException
     * @throws Throwable
     * <AUTHOR>
     * @date   2024/8/30 14:27
     */
    public function statusTransfer($params)
    {
        validate(WorkItemsValidate::class)->scene('statusTransfer')->check(
            $params
        );

        Db::startTrans();
        try {
            $model = WorkItemsModel::findById($params['cnt_id']);
            if ( ! $model) {
                throw new  NotFoundException();
            }
            if ($model->isTask()) {
                throw new ParamsException("任务不可设置状态流转！");
            }
            $commentType = $this->getSettingsTypeByCntType($model->cnt_type);
            $endStatusList = $this->getEndStatusFormatKey($commentType, $model->extends['project_id']);
            $data = array_merge($model->toDetail(), ['status_enum_id' => $params['status_enum_id']]);

            if ($model->isFlaw()) { //缺陷是通过流转到的状态写死的几个固定状态填充对应值的
                $params = $this->flawStatusFlowLinkData($params);
            }

            if ($this->isEnd($data, $model->extends['cnt_type'], $endStatusList)) {
                if ($model->isDemand()) {
                    $params['finish_time'] = date('Y-m-d H:i:s');
                }
                $params['isEnd'] = true;
            } else {
                $params['isEnd'] = false;
            }

            $workCommentId = 0;
            if ($params['content'] ?? false) {
                $workComment = WorkCommentModel::create([
                    'work_items_id' => $params['cnt_id'],
                    'content'       => $params['content'],
                    'remark'        => $params['remark'] ?? '',
                    'comment_type'  => $commentType
                ]);
                $workCommentId = $workComment->comment_id;
            }
            unset($params['content']);
            unset($params['remark']);

            $model->save($params, null, $workCommentId);

            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw  $e;
        }
    }

    /**
     * 设置缺陷状态流转为结束 (优先"已关闭"状态，当没有"已关闭"时，默认进入第一个结束状态)
     * @param  WorkItemsModel  $model
     * @return void
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws Throwable
     * @throws MissingParameterException
     * <AUTHOR>
     * @date   2024/12/9 下午4:29
     */
    public function setFlawStatusEnd(WorkItemsModel $model)
    {

        if ($model->isTask()) {
            throw new ParamsException("任务不可设置状态流转！");
        }

        $statusList = FlowStatusTextModel::selectFlowStatusByStatusId((int)$model->extends['flow_status_id']);
        $statusEnumList = FlowStatusEnumModel::findListById($statusList->column('status_enum_id'));

        $isClose = $statusEnumList->where('name', '=', '已关闭')->first();
        if ($isClose) {
            $endStatus = $statusList->where('status_enum_id', '=', $isClose['status_enum_id'])->first();
        } else {
            $endStatus = $statusList->where('status_type', '=', FlowStatusTextModel::END_STATUS_TYPE)->first();
        }

        if ( ! $endStatus) {
            throw new Exception("未找到对应流程结束状态");
        }

        $this->statusTransfer([
            'cnt_id'         => $model->cnt_id,
            'status_enum_id' => $endStatus['status_enum_id'],
        ]);
    }


    /**
     * 缺陷流转更新每个状态对应的数据
     * @param $data
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date   2024/10/28 14:25
     */
    public function flawStatusFlowLinkData($data)
    {
        $date = date('Y-m-d H:i:s');
        $useId = Ctx::$user->userId;
        $model = FlowStatusEnumModel::findById($data['status_enum_id']);
        if ( ! $model) {
            throw new NotFoundException();
        }
        switch ($model->status_enum_status) {
        case FlowStatusEnumModel::ENUM_STATUS_ACCEPT:
            $data['acceptance_processing_time'] = $date;
            break;
        case FlowStatusEnumModel::ENUM_STATUS_SOLVE:
            $data['resolution_time'] = $date;
            $data['solve_uid'] = $useId;
            break;
        case FlowStatusEnumModel::ENUM_STATUS_VERIFY:
            $data['verification_time'] = $date;
            $data['verify_uid'] = $useId;
            break;
        case FlowStatusEnumModel::ENUM_STATUS_AGAIN:
            $data['reopen_time'] = $date;
            break;
        case FlowStatusEnumModel::ENUM_STATUS_REFUSE:
            $data['rejection_time'] = $date;
            break;
        case FlowStatusEnumModel::ENUM_STATUS_PENDING:
            $data['pending_time'] = $date;
            $data['suspend_uid'] = $useId;
            break;
        case FlowStatusEnumModel::ENUM_STATUS_CLOSE:
            $data['close_time'] = $date;
            $data['closure_uid'] = $useId;
            break;
        }

        return $data;
    }


    /**
     * 关联任务|缺陷
     * @param $params
     * @return void
     * @throws ClientResponseException
     * @throws MissingParameterException
     * @throws ServerResponseException
     * @throws Throwable
     * <AUTHOR>
     * @date   2024/8/30 16:37
     */
    public function associateTasks($params)
    {
        validate(WorkItemsValidate::class)->scene('associateTasks')->check($params);

        Db::startTrans();
        try {
            $model = WorkItemsModel::findById($params['cnt_id']);
            if ( ! $model) {
                throw new  NotFoundException();
            }

            //            if (!$model->isDemand()) {
            //                throw new ParamsException("只有需求可设置关联任务或缺陷！");
            //            }


            //            if ($params['cnt_type'] == WorkItemsModel::CNT_TYPE_TASK) {
            //                $where = [
            //                    ['field_name' => 'cnt_id', 'value' => $params['cnt_id_list'], 'type' => 'selector']
            //                ];
            //
            //                // 检查要关联的任务的迭代ID
            //                $taskList = $this->esLogic->esSearch($where, WorkItemsEsLogic::NOT_PAGE_MAX)->getCollection();
            //                foreach ($taskList as $task) {
            //                    if (($task['iteration_id'] ?? false) && $task['iteration_id'] != ($model->extends['iteration_id'] ?? null)) {
            //                        throw new ParamsException('不能关联已在其他迭代中的任务');
            //                    }
            //                }
            //            }


            $where = [
                ['field_name' => 'parent_id', 'value' => $params['cnt_id'], 'type' => 'term', 'operate_type' => 'equal']
            ];

            if ($params['cnt_type'] == WorkItemsModel::CNT_TYPE_TASK) { //关联任务
                $where[] = [
                    'field_name' => 'cnt_type',
                    'value'      => [
                        WorkItemsModel::CNT_TYPE_TASK,
                        WorkItemsModel::CNT_TYPE_DEMAND
                    ],
                    'type'       => 'selector',
                ];
            } elseif ($params['cnt_type'] == WorkItemsModel::CNT_TYPE_FLAW) { //关联缺陷
                $where[] = [
                    'field_name'   => 'cnt_type',
                    'value'        => WorkItemsModel::CNT_TYPE_FLAW,
                    'type'         => 'term',
                    'operate_type' => 'equal'
                ];
            }


            //获取旧的
            $oldChildList = $this->esLogic->esSearch($where, WorkItemsEsLogic::NOT_PAGE_MAX)->getCollection();

            if ($params['cnt_type'] == WorkItemsModel::CNT_TYPE_TASK) { //关联任务需额外判断
                if ( ! $oldChildList->where('cnt_type', '=', WorkItemsModel::CNT_TYPE_DEMAND)->isEmpty()) {
                    throw new ParamsException("此需求下已有子需求，不可关联任务！");
                }
            }


            $oldChildList = $oldChildList->column('cnt_id');

            //把不存在提交参数中的旧的全部删除
            $removeChildList = array_diff($oldChildList, $params['cnt_id_list']);
            $newChildList = array_diff($params['cnt_id_list'], $oldChildList);

            if ($removeChildList) {
                WorkItemsModel::where([
                    'cnt_id'    => $removeChildList,
                    'is_delete' => BaseModel::DELETE_NOT
                ])
                    ->select()->each(function (WorkItemsModel $model) use ($params) {
                        $model->save(['parent_id' => 0]);
                    });
            }

            if ($newChildList) {
                WorkItemsModel::where([
                    'cnt_id'    => $newChildList,
                    'is_delete' => BaseModel::DELETE_NOT
                ])
                    ->select()->each(
                        function (WorkItemsModel $model) use ($params) {
                            $model->save(['parent_id' => $params['cnt_id']]);
                        }
                    );
            }


            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw  $e;
        }
    }

    /**
     * 任务批量关联流程节点
     * @param $params
     * @return void
     * @throws ClientResponseException
     * @throws MissingParameterException
     * @throws ServerResponseException
     * @throws Throwable
     * <AUTHOR>
     * @date   2024/8/30 16:37
     */
    public function flowNodeAssociateTasks($params)
    {
        validate(WorkItemsValidate::class)->scene('flowNodeAssociateTasks')
            ->check($params);

        $model = IterationProcessNodeModel::findById($params['node_id']);
        if ( ! $model) {
            throw new  NotFoundException("迭代节点不存在");
        }

        $iteration_id = $model->iteration_id;
        Db::startTrans();
        try {

            if ($params['cnt_id_list']) {
                //查询出是否有以被关联了的任务
                $where = [
                    ['field_name' => 'cnt_id', 'value' => $params['cnt_id_list'], 'type' => 'selector'],
                    ['field_name' => 'cnt_type', 'value' => [WorkItemsModel::CNT_TYPE_TASK], 'type' => 'selector'],
                    ['field_name' => 'iteration_process_node_id', 'value' => $params['node_id'], 'type' => 'term', 'operate_type' => 'not_equal'],
                    ['field_name' => 'iteration_process_node_id', 'value' => $params['node_id'], 'exists' => 1],
                ];

                $new = $this->esLogic->esSearch($where, WorkItemsEsLogic::NOT_PAGE_MAX, 1)->getCollection()->column('iteration_process_node_id');
                if ($new) {
                    throw new ParamsException("选中的任务中已有所属节点，请刷新数据重试！");
                }


                //查询出是否有含有父需求的任务
                $where = [
                    ['field_name' => 'cnt_id', 'value' => $params['cnt_id_list'], 'type' => 'selector'],
                    ['field_name' => 'cnt_type', 'value' => [WorkItemsModel::CNT_TYPE_TASK], 'type' => 'selector'],
                    ['field_name' => 'parent_id', 'exists' => 1],
                ];

                $hasParentIdList = $this->esLogic->esSearch($where, WorkItemsEsLogic::NOT_PAGE_MAX, 1, ['parent_id'])->getCollection()->toArray();
                if ($hasParentIdList) {
                    foreach ($hasParentIdList as $parent) {

                        if (($parent['iteration_id'] ?? false) && $parent['iteration_id'] != $iteration_id) {
                            throw new ParamsException("关联的任务父需求不属于本迭代！");
                        }
                    }
                }
            }


            $oldWhere = [
                [
                    'field_name'   => 'iteration_process_node_id',
                    'value'        => $params['node_id'],
                    'type'         => 'term',
                    'operate_type' => 'equal'
                ],
            ];

            //获取旧的
            $oldChildList = $this->esLogic->esSearch($oldWhere, WorkItemsEsLogic::NOT_PAGE_MAX)->getCollection();


            $oldChildList = $oldChildList->column('cnt_id');

            //把不存在提交参数中的旧的全部删除
            $removeChildList = array_diff($oldChildList, $params['cnt_id_list']);
            $newChildList = array_diff($params['cnt_id_list'], $oldChildList);

            if ($removeChildList) {
                WorkItemsModel::where(['cnt_id' => $removeChildList, 'is_delete' => BaseModel::DELETE_NOT])
                    ->select()
                    ->each(function (WorkItemsModel $model) use ($params) {
                        $model->save(['iteration_process_node_id' => null]);
                    });
            }

            if ($newChildList) {
                WorkItemsModel::where(['cnt_id' => $newChildList, 'is_delete' => BaseModel::DELETE_NOT])
                    ->select()
                    ->each(function (WorkItemsModel $model) use ($params, $iteration_id) {
                        $model->save([
                            'iteration_process_node_id' => $params['node_id'],
                            'iteration_id'              => $iteration_id,
                        ]);
                    });
            }


            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw  $e;
        }
    }


    /**
     * 判断工作项是否有子集
     * @param $params
     * @return bool[]
     * <AUTHOR>
     * @date   2024/8/31 14:28
     */
    public function hasChildren($params)
    {
        $result = $this->esLogic->esSearch([
            [
                'field_name'   => 'parent_id',
                'value'        => $params['cnt_id'],
                'type'         => "term",
                'operate_type' => "equal"
            ],
        ], 20, 1, null)->getCollection();

        return [
            'hasChildren' => $result->isEmpty() ? 0 : 1,
        ];
    }

    /**
     * 需求下拉数据
     * 处于结束状态的需求不可选
     * 已有任务的需求不可选
     * 已规划迭代的需求不可选
     * @param $params
     * @return Paginator|Bootstrap
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/11/12 17:06
     */
    public function demandSelector($params)
    {

        $where = [
            ['field_name' => 'cnt_type', 'value' => WorkItemsModel::CNT_TYPE_DEMAND, 'type' => 'term', 'operate_type' => 'equal']
        ];

        if ( ! is_array($params['project_id'])) {
            $params['project_id'] = [$params['project_id']];
        }
        if ($params['project_id'] ?? false) {
            //项目id
            $where[] = ['field_name' => 'project_id', 'value' => $params['project_id'], 'type' => 'selector'];
        }

        if ($params['scenario'] ?? false) {
            switch ($params['scenario']) {
            case 1: //需求
                $where[] = ['field_name' => 'isEnd', 'value' => false, 'type' => 'term', 'operate_type' => 'equal'];
                $where[] = ['field_name' => 'subset_cnt_type', 'value' => [0, WorkItemsModel::CNT_TYPE_DEMAND], 'type' => 'selector'];
                $where[] = ['field_name' => 'iteration_id', 'exists' => 0];
                break;
            case 2: //任务
            case 3: //缺陷
                $where[] = ['field_name' => 'isEnd', 'value' => false, 'type' => 'term', 'operate_type' => 'equal'];
                $where[] = ['field_name' => 'subset_cnt_type', 'value' => [0, WorkItemsModel::CNT_TYPE_TASK], 'type' => 'selector'];
                break;
            case 4: //测试用例
                break;
            }
        }

        if ($params['title'] ?? false) {
            $where[] = ['field_name' => 'title', 'value' => $params['title'], 'type' => 'text'];
        }


        ['page' => $page, 'list_rows' => $listRows] = getPageSize();
        return $this->esLogic->esSearch($where, $listRows, $page)->map(
            function ($v) {
                return ['label' => $v['title'], 'value' => $v['cnt_id']];
            }
        );
    }

    /**
     * 任务需求下拉
     * @param $params
     * @return Collection|\think\model\Collection
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/11/12 17:09
     */
    public function taskDemandSelector($params)
    {

        $where = [];
        if ($params['project_id'] ?? false) {

            if ( ! is_array($params['project_id'])) {
                $params['project_id'] = [$params['project_id']];
            }

            //项目id
            $where[] = ['field_name' => 'project_id', 'value' => $params['project_id'], 'type' => 'selector'];
        }

        return $this->esLogic->esSearch(array_merge([
            ['field_name' => 'cnt_type', 'value' => WorkItemsModel::CNT_TYPE_DEMAND, 'type' => 'term', 'operate_type' => 'equal'],
            ['field_name' => 'isEnd', 'value' => false, 'type' => 'term', 'operate_type' => 'equal'],
            ['field_name' => 'subset_cnt_type', 'value' => [0, WorkItemsModel::CNT_TYPE_TASK], 'type' => 'selector'],
        ], $where), WorkItemsEsLogic::NOT_PAGE_MAX)->getCollection()->map(
            function ($v) {
                return ['label' => $v['title'], 'value' => $v['cnt_id']];
            }
        );
    }

    /**
     * 测试用例需求下拉
     * @param $params
     * @return Collection|\think\model\Collection
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/11/12 17:09
     */
    public function testCaseDemandSelector($params)
    {

        $where = [];
        if ($params['project_id'] ?? false) {
            //项目id
            $where[] = [
                'field_name'   => 'project_id',
                'value'        => $params['project_id'],
                'type'         => 'term',
                'operate_type' => 'equal'
            ];
        }

        return $this->esLogic->esSearch(array_merge([
            ['field_name' => 'cnt_type', 'value' => WorkItemsModel::CNT_TYPE_DEMAND, 'type' => 'term', 'operate_type' => 'equal'],
        ], $where), WorkItemsEsLogic::NOT_PAGE_MAX)->getCollection()->map(
            function ($v) {
                return ['label' => $v['title'], 'value' => $v['cnt_id']];
            }
        );
    }

    /**
     * 获取日期列表
     * @return array
     * @throws DateMalformedStringException
     * <AUTHOR>
     * @date   2024/11/13 22:11
     */
    public function getDataList($startDate, $endDate): array
    {
        // 创建 DateTime 对象
        $start = new DateTime($startDate);
        $end = new DateTime($endDate);

        // 循环遍历日期范围
        $dates = [];
        while ($start <= $end) {
            // 将当前日期加入数组
            $dates[] = $start->format('Y-m-d');

            // 增加一天
            $start->modify('+1 day');
        }

        return $dates;
    }

    /**
     * 处理es查询返回数据
     * @param $response
     * @return array
     * <AUTHOR>
     * @date   2024/9/6 16:28
     */
    private function handleEsResponse($response)
    {
        $resp = json_decode((string)$response, true);
        //        $total = $resp['hits']['total']['value'];
        $data = $resp['hits']['hits'];
        $data = array_column($data, '_source');

        return $data;
    }

    /**
     * 拖拽
     * @return array
     * <AUTHOR>
     * @date   2024/11/11 09:42
     */
    public function dragToIteration($iterationId, array $cntIdList)
    {
        if ( ! $cntIdList) {
            throw new ParamsException("工作项id集合不可为空");
        }
        if ($iterationId) {
            $iteration = IterationModel::findById($iterationId);
            if ( ! $iteration) {
                throw new NotFoundException();
            }
        } else {
            $iterationId = null;
        }

        $modelList = WorkItemsModel::findListById($cntIdList);


        $errorMsgMask = 0; //错误消息掩码
        $parentErrMsg = 1;
        $taskErrMsg = 2;
        $taskErrMsg2 = 4;

        $msgList = [
            $parentErrMsg => '父需求不支持规划迭代',
            $taskErrMsg   => '关联需求的任务不支持规划迭代',
            $taskErrMsg2  => '节点任务不支持规划',
        ];
        $sussCount = 0;
        $failCount = 0;
        $result = [
            'failed'  => [
                'demand' => [],
                'task'   => [],
                'flaw'   => [],
            ],
            'success' => [
                'demand' => [],
                'task'   => [],
                'flaw'   => [],
            ]
        ];
        foreach ($modelList as $v) {
            switch ($v['cnt_type']) {
            case WorkItemsModel::CNT_TYPE_DEMAND:
                if (
                    $v->extends['subset_cnt_type']
                    == WorkItemsModel::CNT_TYPE_DEMAND
                ) {
                    $errorMsgMask |= $parentErrMsg;
                    $failCount += 1;
                    $result['failed']['demand'][] = $v->cnt_id;
                } else {
                    $v->save(['iteration_id' => $iterationId]);
                    $sussCount += 1;
                    $result['success']['demand'][] = $v->cnt_id;
                }
                break;
            case WorkItemsModel::CNT_TYPE_TASK:
                if ($v->extends['parent_id']) {
                    $errorMsgMask |= $taskErrMsg;
                    $failCount += 1;
                    $result['failed']['task'][] = $v->cnt_id;
                } elseif ($v->extends['iteration_process_node_id'] ?? false) {
                    $errorMsgMask |= $taskErrMsg2;
                    $failCount += 1;
                    $result['failed']['task'][] = $v->cnt_id;
                } else {
                    $v->save(['iteration_id' => $iterationId]);
                    $sussCount += 1;
                    $result['success']['task'][] = $v->cnt_id;
                }
                break;
            case WorkItemsModel::CNT_TYPE_FLAW:
                $v->save(['iteration_id' => $iterationId]);
                $sussCount += 1;
                $result['success']['flaw'][] = $v->cnt_id;
                break;
            default:
                throw new ParamsException();
            }
        }
        $msg = '';
        if (count($cntIdList) == 1 && $sussCount) { //单条更新
            $msg .= "规划成功。";
        } else {
            $msg .= sprintf(
                "批量规划成功%s条，失败%d条。",
                $sussCount,
                $failCount
            );
        }
        $errorMsgList = [];
        if ($errorMsgMask & $parentErrMsg) {
            $errorMsgList[] = $msgList[$parentErrMsg];
        }
        if ($errorMsgMask & $taskErrMsg) {
            $errorMsgList[] = $msgList[$taskErrMsg];
        }
        if ($errorMsgMask & $taskErrMsg2) {
            $errorMsgList[] = $msgList[$taskErrMsg2];
        }
        if ($errorMsgList) {
            $msg .= '失败原因：'.implode('，', $errorMsgList);
        }

        return [
            'msg'      => $msg,
            'data'     => $result,
            'msg_type' => ! $sussCount ? 2 : 1, //1成功消息，2提醒消息。成功数为0用提醒消息
        ];
    }

    /**
     * 工作项个数以及已结束个数
     * @param $iterationId
     * @return array[]
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/11/13 11:31
     */
    public function countGroupCntType($iterationId)
    {

        $where = [
            [
                'field_name'   => 'iteration_id',
                'value'        => $iterationId,
                'type'         => "term",
                'operate_type' => "equal"
            ]
        ];

        $topItem = $this->esLogic->countGroupCntType($where);

        return [
            [
                'label'     => '需求',
                'cnt_type'  => WorkItemsModel::CNT_TYPE_DEMAND,
                'total'     => $topItem[WorkItemsModel::CNT_TYPE_DEMAND]['doc_count']
                    ?? 0,
                'total_end' => $topItem[WorkItemsModel::CNT_TYPE_DEMAND]['not_end']['doc_count']
                    ??
                    0,
            ],
            [
                'label'     => '任务',
                'cnt_type'  => WorkItemsModel::CNT_TYPE_TASK,
                'total'     => $topItem[WorkItemsModel::CNT_TYPE_TASK]['doc_count']
                    ?? 0,
                'total_end' => $topItem[WorkItemsModel::CNT_TYPE_TASK]['not_end']['doc_count']
                    ?? 0,
            ],
            [
                'label'     => '缺陷',
                'cnt_type'  => WorkItemsModel::CNT_TYPE_FLAW,
                'total'     => $topItem[WorkItemsModel::CNT_TYPE_FLAW]['doc_count']
                    ?? 0,
                'total_end' => $topItem[WorkItemsModel::CNT_TYPE_FLAW]['not_end']['doc_count']
                    ?? 0,
            ],
        ];
    }


    /**
     * 获取迭代开始结束时间
     * @param $iterationId
     * @return array
     * <AUTHOR>
     * @date   2024/11/14 10:40
     */
    private function getIterationDate($iterationId)
    {
        $model = IterationModel::findById($iterationId);
        if ( ! $model) {
            throw new  NotFoundException();
        }

        $startTime = date("Y-m-d 00:00:00");
        $endTime = date("Y-m-d 23:59:59");

        return [
            'start' => $model['estimate_start_time'] == "1971-01-01 00:00:00"
                ? $startTime
                : "{$model['estimate_start_time']}",
            'end'   => $model['estimate_end_time'] == "1971-01-01 00:00:00"
                ? $endTime
                : str_replace(
                    '00:00:00',
                    '23:59:59',
                    $model['estimate_end_time']
                ),
        ];
    }

    /**
     * 工作项燃尽图
     * @param $iterationId
     * @return array[]
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/11/13 11:31
     */
    public function workItemBurndownChart($iterationId)
    {

        $where = [
            ['field_name' => 'iteration_id', 'value' => $iterationId, 'type' => "term", 'operate_type' => "equal"]
        ];

        $date = $this->getIterationDate($iterationId);
        $startTime = $date['start'];
        $endTime = $date['end'];
        $dataList = $this->getDataList($startTime, $endTime);

        $topItem = $this->esLogic->workItemBurndownChart($where, $endTime);
        $result = [];
        foreach ($topItem as $v) {
            //            if ((strtotime($v['key']) > strtotime($endTime)) || (strtotime($v['key']) < strtotime($startTime))) {
            //                continue;
            //            }
            $cnt_type_count = array_column(
                $v['cnt_type_count']['buckets'],
                'doc_count',
                'key'
            );
            $result[$v['key']] = [
                'date'   => $v['key'],
                'demand' => $cnt_type_count[WorkItemsModel::CNT_TYPE_DEMAND] ??
                    0,
                'task'   => $cnt_type_count[WorkItemsModel::CNT_TYPE_TASK] ?? 0,
                'flaw'   => $cnt_type_count[WorkItemsModel::CNT_TYPE_FLAW] ?? 0,
            ];
        }

        $result2 = [];
        foreach ($dataList as $v) {
            $result2[] = [
                'date'   => $v,
                'demand' => $result[$v]['demand'] ?? 0,
                'task'   => $result[$v]['task'] ?? 0,
                'flaw'   => $result[$v]['flaw'] ?? 0,
            ];
        }


        return $result2;
    }


    /**
     * 工时燃尽图
     * @param $iterationId
     * @return array[]
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/11/13 11:31
     */
    public function laborHoursBurndownChart($iterationId, $cntTypeList)
    {

        $where = [
            ['field_name' => 'iteration_id', 'value' => $iterationId, 'type' => "term", 'operate_type' => "equal"],
            ['field_name' => 'cnt_type', 'value' => $cntTypeList, 'type' => "selector"],
            //            ['field_name' => 'iteration_id', 'value' => 349, 'type' => "term", 'operate_type' => "equal"],
            [
                'field_name' => 'cnt_type',
                'value'      => [
                    WorkItemsModel::CNT_TYPE_TASK,
                    WorkItemsModel::CNT_TYPE_FLAW
                ],
                'type'       => "selector"
            ],
        ];

        $date = $this->getIterationDate($iterationId);
        $startTime = $date['start'];
        $endTime = $date['end'];


        $dataList = $this->getDataList($startTime, $endTime);
        //        dd($dataList);


        $cntIdList = $this->esLogic->laborHoursBurndownChart($where);

        $estimatedWorkHours = 0;
        $actualWorkHours = new Collection();
        $remainingWork = new Collection();

        $workHoursList = WorkHoursModel::findListByCntId($cntIdList);

        $workHoursList->each(function ($v) use (
            &$estimatedWorkHours,
            &$actualWorkHours,
            &$remainingWork
        ) {
            switch ($v['type']) {
            case WorkHoursModel::TYPE_ESTIMATED:
                $estimatedWorkHours += $v['working_hours'];
                break;
            case WorkHoursModel::TYPE_ACTUAL:
                $actualWorkHours->push([
                    'working_hours' => $v['working_hours'],
                    'work_date'     => $v['work_date'],
                ]);
                break;
            case WorkHoursModel::TYPE_REMAINING:
                $remainingWork->push([
                    'working_hours' => $v['working_hours'],
                    'work_date'     => explode(' ', $v['create_at'])[0],
                ]);
                break;
            default:
                throw new Exception();
            }
        });

        $result = [];
        foreach ($dataList as $v) {
            $completed_time = array_sum(
                $actualWorkHours->where('work_date', '<=', $v)
                    ->column('working_hours')
            );
            $surplus_hours = array_sum(
                $remainingWork->where('work_date', '<=', $v)
                    ->column('working_hours')
            );
            $surplus_hours2 = ($estimatedWorkHours - $completed_time)
                + $surplus_hours;
            $result[] = [
                'date'           => $v,
                'completed_time' => $completed_time,
                'surplus_hours'  => $surplus_hours2 < 0 ? 0 : $surplus_hours2,
            ];
        }


        return $result;
    }

    /**
     * 获取在指定迭代下的工作项id集合
     * @param  int  $iterationId
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * User Long
     * Date 2024/11/18
     */
    public static function getItemByIterationId(int $iterationId)
    {
        return Es::getInstance()
            ->setQueryParam([
                'field_name'   => 'iteration_id',
                'value'        => $iterationId,
                'type'         => 'term',
                'operate_type' => 'equal'
            ])
            ->setPage(WorkItemsEsLogic::NOT_PAGE_MAX)
            ->getPaginator()
            ->getCollection()
            ->column('cnt_id');
    }

    /**
     * 批量设置工作项迭代
     * @param  array  $cntIds       工作项id合集
     * @param  int    $iterationId  迭代id
     * @param  array  $flowStatus   工作流数据
     *                              $flowStatus['flow_status_id'] = 工作流id;
     *                              $flowStatus['status_enum_id'] = 开始状态id;
     * @throws ClientResponseException
     * @throws MissingParameterException
     * @throws ServerResponseException
     * @throws Throwable
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     *                              User Long
     *                              Date 2024/9/4
     */
    public static function setItemsIterationId(array $cntIds, int $iterationId = null)
    {
        try {
            DBTransaction::begin();

            $data['iteration_id'] = $iterationId;

            WorkItemsModel::where(
                ['cnt_id' => $cntIds, 'is_delete' => BaseModel::DELETE_NOT]
            )
                ->select()->each(function (WorkItemsModel $model) use ($data) {
                    $model->save($data);
                });

            DBTransaction::commit();
        } catch (Throwable $e) {
            DBTransaction::rollback();
            throw  $e;
        }
    }

    /**
     * 获取未完成的任务id集合
     * @param  int   $iterationProcessNodeId
     * @param  bool  $isEnd
     * @param  bool  $isHandlerUid
     * @param  bool  $isOnlyTransferableAfterCompletion
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * User Long
     * Date 2024/11/19
     */
    public static function getIterationNodeTaskIdByNodeId(int $iterationProcessNodeId, bool $isEnd = null, bool $isOnlyTransferableAfterCompletion = null, bool $isHandlerUid = true)
    {
        $es = Es::getInstance()
            ->setQueryParam([
                'field_name'   => 'cnt_type',
                'value'        => WorkItemsModel::CNT_TYPE_TASK,
                'type'         => 'term',
                'operate_type' => 'equal'
            ])
            ->setQueryParam([
                'field_name'   => 'iteration_process_node_id',
                'value'        => $iterationProcessNodeId,
                'type'         => 'term',
                'operate_type' => 'equal'
            ]);

        if (is_bool($isEnd)) {
            $es = $es->setQueryParam([
                'field_name'   => 'isEnd',
                'value'        => $isEnd,
                'type'         => 'term',
                'operate_type' => 'equal'
            ]);
        }

        if ( ! $isHandlerUid) {
            $es = $es->setQueryParam(['field_name' => 'handler_uid', 'exists' => false]);
        }

        if (is_bool($isOnlyTransferableAfterCompletion)) {
            $es = $es->setQueryParam([
                'field_name'   => 'only_transferable_after_completion',
                'value'        => $isOnlyTransferableAfterCompletion,
                'type'         => 'term',
                'operate_type' => 'equal'
            ]);
        }

        return $es->setPage(WorkItemsEsLogic::NOT_PAGE_MAX)
            ->getPaginator()
            ->getCollection()
            ->column('cnt_id');
    }

    /**
     * 工时燃烧报告
     * @param $iterationId
     * @return array
     * @throws ClientResponseException
     * @throws Exception
     * @throws ServerResponseException
     * @throws DateMalformedStringException
     * <AUTHOR>
     * @date   2024/12/5 下午4:22
     */
    public function manHourBurnReport($iterationId)
    {

        //得到日期区间
        $date = $this->getIterationDate($iterationId);
        $startTime = $date['start'];
        $endTime = $date['end'];

        $dateList = $this->getDataList($startTime, $endTime);


        //获取所有数据
        $where = [
            ['field_name' => 'iteration_id', 'value' => $iterationId, 'type' => "term", 'operate_type' => "equal"],
            ['field_name' => 'cnt_type', 'value' => [WorkItemsModel::CNT_TYPE_TASK, WorkItemsModel::CNT_TYPE_FLAW], 'type' => "selector"],
        ];
        $parents = $this->esLogic->getParentsByParams($where);
        $dataList = $this->esLogic->esSearch([
            ['field_name' => 'cnt_id', 'value' => $parents, 'type' => "selector"]
        ], $this->esLogic::NOT_PAGE_MAX, 1, [
            'title',
            'handler_uid',
            'estimated_work_hours',
            'actual_work_hours',
            'remaining_work',
            'exceeding_working_hours',
        ], [
            'cnt_id' => 'desc',
        ])->each(function ($v) use ($dateList) {
            $v['estimated_work_hours'] = $v['estimated_work_hours'] ?? 0;
            $v['actual_work_hours'] = $v['actual_work_hours'] ?? 0;
            $v['remaining_work'] = $v['remaining_work'] ?? 0;
            $v['exceeding_working_hours'] = $v['exceeding_working_hours'] ?? 0;

            switch ($v['cnt_type']) {
            case  WorkItemsModel::CNT_TYPE_DEMAND:
            case  WorkItemsModel::CNT_TYPE_TASK:
                $v['node_id'] = $v['cnt_id'];
                $v['node_pid'] = $v['parent_id'];
                break;
            case  WorkItemsModel::CNT_TYPE_FLAW:
                $v['node_id'] = $v['cnt_id'];
                $v['node_pid'] = 0;
                break;
            default:
                throw new Exception();
            }


            $v['date_list'] = [];
            foreach ($dateList as $s) {
                $v['date_list'][$s] = [
                    'date'           => $s,
                    'remaining_work' => '',
                    'type'           => 0,
                ];
            }


            return $v;
        })->column(null, 'cnt_id');

        //获取所有工时数据
        $workHoursListGroupByCntId = [];
        WorkHoursModel::findListByCntId(array_keys($dataList))->each(function ($v) use (&$workHoursListGroupByCntId) {
            if (isset($workHoursListGroupByCntId[$v['cnt_id']])) {
                $workHoursListGroupByCntId[$v['cnt_id']]->push($v);
            } else {
                $workHoursListGroupByCntId[$v['cnt_id']] = (new Collection([$v]));
            }
            return $v;
        });

        $currentDate = date('Y-m-d');

        //对每条数据的工时进行处理
        foreach ($dataList as &$v) {
            /** @var Collection $workHoursList */
            $workHoursList = $workHoursListGroupByCntId[$v['cnt_id']] ?? null;
            if ( ! $workHoursList) {
                continue;
            }

            $truncatedArray = $v['date_list'];

            foreach ($truncatedArray as &$q) {
                if ($q['date'] > $currentDate) {
                    continue;
                }
                //当前以及之前的所有实际工时总和
                $allActualWorkHours = array_sum(
                    $workHoursList
                        ->where('work_date', '<=', $q['date'])
                        ->where('type', '=', WorkHoursModel::TYPE_ACTUAL)
                        ->column('working_hours'),
                );
                //当前以及之前的所有剩余工时总和
                $allRemainingWork = array_sum(
                    $workHoursList
                        ->where('create_at', '<=', $q['date'].' 23:59:59')
                        ->where('type', '=', WorkHoursModel::TYPE_REMAINING)
                        ->column('working_hours'),
                );
                //当天实际工时总和
                $actualWorkHours = array_sum(
                    $workHoursList
                        ->where('work_date', '=', $q['date'])
                        ->where('type', '=', WorkHoursModel::TYPE_ACTUAL)
                        ->column('working_hours')
                );

                //所有预估工时
                $estimated_work_hours = array_sum(
                    $workHoursList
                        //                        ->where('create_at', '<=', $q['date'].' 23:59:59')
                        ->where('type', '=', WorkHoursModel::TYPE_ESTIMATED)
                        ->column('working_hours')
                );


                $toDayRemainingWork = (float)bcsub(bcadd((string)$estimated_work_hours, (string)$allRemainingWork, 2), (string)$allActualWorkHours, 2);

                //当天含有剩余工时（1、填写了预估工时；2、手动添加了剩余工时）或填写了实际工时才会显示
                if ( ! (($toDayRemainingWork > 0) || ($actualWorkHours > 0))) {
                    continue;
                }


                $q['remaining_work'] = number_format(max($toDayRemainingWork, 0), 2, '.', '');
                $this->recursivelyUpdateTheParent($v['node_pid'], $q['date'], $q['remaining_work'], $dataList);
                //填写了实际工时的日期才会有此判断
                $q['type'] = $actualWorkHours ? ($toDayRemainingWork >= 0 ? 1 : 2) : 0;
            }


            $v['date_list'] = array_merge($v['date_list'], $truncatedArray);
        }
        $handlerUidList = [];
        array_map(function ($v) use (&$handlerUidList) {
            $handlerUidList = array_merge($handlerUidList, $v ?? []);
        }, array_column($dataList, 'handler_uid'));

        $handlerUidList = array_unique($handlerUidList);

        $handlerList = ProjectUserModel::selectListById($handlerUidList)->column(null, 'user_id');

        foreach ($dataList as &$v) {
            $v['date_list'] = array_values($v['date_list']);
            $v['handler_list'] = [];
            foreach ($v['handler_uid'] ?? [] as $s) {
                $v['handler_list'][] = [
                    'user_id'   => $handlerList[$s]['user_id'] ?? '',
                    'user_name' => $handlerList[$s]['user_name'] ?? '',
                ];
            }
        }

        //构建树
        $tree = $this->buildTree(array_values($dataList), 'node_id', 'node_pid');

        return ['data' => $tree, 'date_list' => $dateList];
    }

    /**
     * 递归更新父级的指定日期的工时
     * @param $pid
     * @param $date
     * @param $value
     * @param $data
     * @return void
     * <AUTHOR>
     * @date   2024/12/5 15:34
     */
    private function recursivelyUpdateTheParent($pid, $date, $value, &$data)
    {
        if ( ! isset($data[$pid])) {
            return;
        }
        if ($data[$pid]['date_list'][$date]['remaining_work'] == '') {
            $data[$pid]['date_list'][$date]['remaining_work'] = $value;
        } else {
            $data[$pid]['date_list'][$date]['remaining_work'] += $value;
        }
        $data[$pid]['date_list'][$date]['remaining_work'] = number_format((float)$data[$pid]['date_list'][$date]['remaining_work'], 2, '.', '');
        $this->recursivelyUpdateTheParent($data[$pid]['node_pid'], $date, $value, $data);
    }


    /**
     * 工时资源报告
     * @param $iterationId
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws DateMalformedStringException
     * <AUTHOR>
     * @date   2024/12/5 下午8:52
     */
    public function workResourceReports($iterationId)
    {


        //得到日期区间
        $date = $this->getIterationDate($iterationId);
        $startTime = $date['start'];
        $endTime = $date['end'];

        $dateList = $this->getDataList($startTime, $endTime);


        //获取所有数据
        $where = [
            ['field_name' => 'iteration_id', 'value' => $iterationId, 'type' => "term", 'operate_type' => "equal"],
            ['field_name' => 'cnt_type', 'value' => [WorkItemsModel::CNT_TYPE_TASK, WorkItemsModel::CNT_TYPE_FLAW], 'type' => "selector"],
        ];

        $dataList = $this->esLogic->workResourceReports($where, [
            'title',
            'estimated_work_hours',
            'remaining_work',
            'exceeding_working_hours',
            'cnt_type',
            'status_enum_id',
            'type_id',
        ]);
        $data = new Collection($dataList['data']);
        //预估工时
        //剩余工时
        //超出工时 添加默认值0
        $data = $data->each(function ($v) {
            $v = array_merge([
                'estimated_work_hours'    => 0,
                'remaining_work'          => 0,
                'exceeding_working_hours' => 0,
            ], $v);
            return $v;
        });
        $result = [];

        $dateListTemp = [];
        foreach ($dateList as $s) {
            $dateListTemp[$s] = [
                'date'              => $s,
                'actual_work_hours' => '',
                'cnt_list'          => [],
            ];
        }

        foreach ($dataList['statistics'] as $v) {
            $result[] = [
                'user_id'                               => $v['key'],
                'total_estimated_work'                  => $v['all_work_hour']['value'] ? number_format($v['all_work_hour']['value'], 2) : 0,
                'total_estimated_work_cnt_list'         => array_values($data->where('cnt_id', 'in', array_column($v['cnt_id_list']['buckets'], 'key'))->toArray()),
                'total_estimated_work_not_end'          => $v['not_end_work_hour']['all_work_hour']['value'] ? number_format($v['not_end_work_hour']['all_work_hour']['value'], 2) : 0,
                'total_estimated_work_not_end_cnt_list' => array_values($data->where('cnt_id', 'in', array_column($v['not_end_work_hour']['cnt_id_list']['buckets'], 'key'))->toArray()),
                'date_list'                             => $dateListTemp,
            ];
        }


        usort($result, function ($a, $b) {
            return intval((int)$a['user_id'] < (int)$b['user_id']);
        });


        $workHoursList = WorkHoursModel::findListByCntId($data->column('cnt_id'));
        $handlerList = ProjectUserModel::selectListById(array_column($result, 'user_id'))->column(null, 'user_id');

        foreach ($result as &$v) {
            $v['user_name'] = $handlerList[$v['user_id']]['user_name'] ?? '--';
            $currentUserWorkHoursList = $workHoursList->where('create_by', '=', $v['user_id'])->where('type', '=', WorkHoursModel::TYPE_ACTUAL);


            $v['total_actual_work_hours'] = array_sum($currentUserWorkHoursList->column('working_hours')) ? number_format(array_sum($currentUserWorkHoursList->column('working_hours')), 2) : 0;

            $total_actual_work_hours_cnt_list = [];
            $cntList = $data->where('cnt_id', 'in', $currentUserWorkHoursList->column('cnt_id'))->column(null, 'cnt_id');

            $currentUserWorkHoursList->each(function ($s) use (&$total_actual_work_hours_cnt_list, $cntList, &$v) {
                $total_actual_work_hours_cnt_list[] = [
                    'actual_work_hours' => $s['working_hours'],
                    'work_date'         => $s['work_date'],
                    'cnt_id'            => $s['cnt_id'],
                    'title'             => $cntList[$s['cnt_id']]['title'],
                    'cnt_type'          => $cntList[$s['cnt_id']]['cnt_type'] ?? null,
                    'status_enum_id'    => $cntList[$s['cnt_id']]['status_enum_id'] ?? null,
                    'type_id'           => $cntList[$s['cnt_id']]['type_id'] ?? null,
                ];

                if ( ! isset($v['date_list'][$s['work_date']])) {
                    return;
                }

                if ($v['date_list'][$s['work_date']]['actual_work_hours'] == '') {
                    $v['date_list'][$s['work_date']]['actual_work_hours'] = 0;
                }
                $v['date_list'][$s['work_date']]['actual_work_hours'] += $s['working_hours'];
                if ($v['date_list'][$s['work_date']]['actual_work_hours'] !== '') { // 检查它是否不是一个空字符串
                    // 在四舍五入之前确保它是一个数值，然后四舍五入到两位小数
                    $current_hours = (float)$v['date_list'][$s['work_date']]['actual_work_hours'];
                    $v['date_list'][$s['work_date']]['actual_work_hours'] = round($current_hours, 2);
                } else {
                    // 如果它是一个空字符串，也许它应该保持为0或被显式设置。
                    // 如果它是一个空字符串并且打算用作数字，则假定为0。
                    $v['date_list'][$s['work_date']]['actual_work_hours'] = 0.0;
                }
                $v['date_list'][$s['work_date']]['cnt_list'][] = [
                    'actual_work_hours' => $s['working_hours'],
                    'cnt_id'            => $s['cnt_id'],
                    'title'             => $cntList[$s['cnt_id']]['title'],
                    'cnt_type'          => $cntList[$s['cnt_id']]['cnt_type'] ?? null,
                    'status_enum_id'    => $cntList[$s['cnt_id']]['status_enum_id'] ?? null,
                    'type_id'           => $cntList[$s['cnt_id']]['type_id'] ?? null,
                ];
            });


            $v['total_actual_work_hours_cnt_list'] = $total_actual_work_hours_cnt_list;
            $v['date_list'] = array_values($v['date_list']);
        }

        return ['data' => $result, 'date_list' => $dateList];
    }

    /**
     * 查询指定用户的实际工时信息，按日期分组，并填充工作项内容。
     * 类似原型图的看板视图。
     *
     * @param  array  $params  查询参数:
     *                         - project_id (int, required): 项目ID
     *                         - start_date (string, required, 'YYYY-MM-DD'): 开始日期
     *                         - end_date (string, required, 'YYYY-MM-DD'): 结束日期
     *                         - user_ids (array, required): 用户ID数组
     *                         - cnt_type_list (array, optional): 工作项类型过滤 (1:需求, 2:任务, 3:缺陷)
     *                         - field_list (array, optional): 需要返回的工作项字段
     * @return array 格式化后的工时数据
     * @throws ParamsException
     * @throws Exception
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function queryUserWorkHoursByDateRange(array $params): array
    {
        validate(WorkItemsValidate::class)->scene('queryUserWorkHours')->check($params);

        // 1. 获取核心数据
        $primaryData = $this->fetchPrimaryData($params);
        if ($primaryData['workHoursRecords']->isEmpty()) {
            $structure = [];
            if ( ! empty($params['group_by_field'])) {
                // For any grouping type, create a single default group as requested.
                $structure['_default_group_'] = [
                    'group_key'       => '_default_group_',
                    'group_label'     => '--',
                    'daily_entries'   => $this->initializeEmptyDateRangeResult($primaryData['dateRange'], true),
                    'work_item_count' => 0
                ];
            }
            return $this->formatReportOutput($structure, $params, $primaryData['dateRange']);
        }

        // 2. 预加载关联数据以提高性能
        $preloadedData = $this->preloadAssociationData($primaryData['workItemsMap'], $params['project_id'], $params['user_ids']);

        // 3. 初始化报告结构
        $reportStructure = $this->buildReportStructure($primaryData, $params);

        // 4. 填充报告数据
        $this->populateReportData($reportStructure, $primaryData, $preloadedData, $params);

        // 5. 格式化并返回最终结果
        return $this->formatReportOutput($reportStructure, $params, $primaryData['dateRange']);
    }

    /**
     * 步骤1：获取核心数据，包括工时记录和关联的工作项详情
     * @param  array  $params
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     */
    private function fetchPrimaryData(array $params): array
    {
        $startDate = $params['start_date'];
        $endDate = $params['end_date'];
        $userIds = $params['user_ids'];

        // 获取日期范围
        $dateRange = $this->getDataList($startDate, $endDate);
        if (empty($dateRange)) {
            return ['workHoursRecords' => new Collection(), 'workItemsMap' => [], 'dateRange' => []];
        }

        // 从数据库查询实际工时记录
        $dbStartDate = str_replace('-', '', $startDate);
        $dbEndDate = str_replace('-', '', $endDate);
        $workHoursRecords = WorkHoursModel::status()
            ->whereIn('create_by', $userIds)
            ->whereBetween('work_date', [$dbStartDate, $dbEndDate])
            ->where('type', WorkHoursModel::TYPE_ACTUAL)
            ->order('work_date asc, create_by asc')
            ->select();

        if ($workHoursRecords->isEmpty()) {
            return ['workHoursRecords' => $workHoursRecords, 'workItemsMap' => [], 'dateRange' => $dateRange];
        }

        // 从工时记录中提取工作项ID
        $workItemsIds = array_unique($workHoursRecords->column('cnt_id'));

        // 准备ES查询参数
        $esTreeSearchParams = $params['searchParams'] ?? ['public_field' => [], 'specific_fields_by_type' => []];
        $esTreeSearchParams['public_field'][] = ['field_name' => 'project_id', 'value' => $params['project_id'], 'type' => 'selector'];
        $esTreeSearchParams['public_field'][] = ['field_name' => 'cnt_id', 'value' => $workItemsIds, 'type' => 'selector'];
        if ( ! empty($params['cnt_type_list'])) {
            $esTreeSearchParams['public_field'][] = ['field_name' => 'cnt_type', 'value' => $params['cnt_type_list'], 'type' => 'selector'];
        }

        // 从ES获取工作项详情，注意：此处不进行分组
        $esQueryResult = $this->esLogic->esSearchSummaryForTree(
            $esTreeSearchParams, false, false, false, [], $statistics,
            $this->formatOrderParams($params['order'] ?? []), null, [], $this->listHandler()
        );

        // 将ES结果转换为Map以便快速查找
        $workItemsMap = [];
        foreach ($esQueryResult as $item) {
            if (isset($item['cnt_id'])) {
                $workItemsMap[$item['cnt_id']] = $item;
            }
        }

        // 过滤掉在ES中找不到的工作项的工时记录
        $workHoursRecords = $workHoursRecords->filter(fn($record) => isset($workItemsMap[$record['cnt_id']]));

        return compact('workHoursRecords', 'workItemsMap', 'dateRange');
    }

    /**
     * 步骤2：预加载所有关联数据
     * @param  array      $workItemsMap
     * @param  int|array  $projectId
     * @param  array      $userIds
     * @return array
     */
    private function preloadAssociationData(array $workItemsMap, $projectId, array $userIds): array
    {
        if (empty($workItemsMap)) {
            return ['users' => [], 'iterations' => [], 'priorities' => [], 'statuses' => []];
        }

        $iterationIds = [];
        $priorityValues = [];
        $statusEnumIds = [];
        $flowStatusIds = [];
        $involvedUserIds = $userIds;

        foreach ($workItemsMap as $item) {
            if ( ! empty($item['iteration_id'])) {
                $iterationIds[] = $item['iteration_id'];
            }
            if ( ! empty($item['priority']) && ! is_array($item['priority'])) {
                $priorityValues[] = (string)$item['priority'];
            }
            if ( ! empty($item['status_enum_id'])) {
                $statusEnumIds[] = $item['status_enum_id'];
            }
            if ( ! empty($item['flow_status_id'])) {
                $flowStatusIds[$item['flow_status_id']][] = $item['status_enum_id'];
            }
            if ( ! empty($item['handler_uid'])) {
                $involvedUserIds = array_merge($involvedUserIds, is_array($item['handler_uid']) ? $item['handler_uid'] : [$item['handler_uid']]);
            }
        }

        $preloaded = [];
        $preloaded['iterations'] = ! empty($iterationIds) ? IterationModel::whereIn('iteration_id', array_unique($iterationIds))->column('iteration_name', 'iteration_id') : [];
        $preloaded['priorities'] = ! empty($priorityValues) ? EnumModel::where('enum_code', 'priority')->whereIn('enum_value', array_unique($priorityValues))->column('enum_name', 'enum_value') : [];
        $preloaded['users'] = ! empty($involvedUserIds) ? ProjectUserModel::whereIn('project_id', is_array($projectId) ? $projectId : [$projectId])->whereIn('user_id', array_unique($involvedUserIds))->column('user_name', 'user_id') : [];

        // 批量加载状态名称
        $statuses = [];
        if ( ! empty($statusEnumIds)) {
            $taskStatusIds = array_filter(array_unique($statusEnumIds), 'is_numeric');
            if ( ! empty($taskStatusIds)) {
                $statuses = FlowStatusEnumModel::whereIn('status_enum_id', $taskStatusIds)->column('name', 'status_enum_id');
            }

//            foreach ($flowStatusIds as $flowId => $enumIds) {
//                $flowStatuses = FlowStatusTextModel::where('flow_status_id', $flowId)->whereIn('status_enum_id', array_unique($enumIds))->select();
//                foreach ($flowStatuses as $status) {
//                    $statuses["{$flowId}-{$status['status_enum_id']}"] = $status['status_text_name'];
//                }
//            }

//            $statuses = $statuses + EnumModel::status()->whereIn('enum_code', [
//                    'notStarted',
//                    'inProgress',
//                    'completed',
//                ])->column('enum_name', 'enum_value');
//            dd($statuses);
        }
        $preloaded['statuses'] = $statuses;

        return $preloaded;
    }

    /**
     * 步骤3：初始化报告的整体结构
     * @param  array  $primaryData
     * @param  array  $params
     * @return array
     */
    private function buildReportStructure(array $primaryData, array $params): array
    {
        $structure = [];
        $groupByField = $params['group_by_field'] ?? null;
        $isGrouped = ! empty($groupByField);

        if ($isGrouped) {
            $allGroupKeys = [];
            foreach ($primaryData['workItemsMap'] as $workItem) {
                if (isset($workItem[$groupByField])) {
                    $keys = is_array($workItem[$groupByField]) ? $workItem[$groupByField] : [$workItem[$groupByField]];
                    foreach ($keys as $key) {
                        if ($key !== null && $key !== '') {
                            $allGroupKeys[$key] = true;
                        }
                    }
                } else {
                    // Handle items where the group field is missing or null
                    $allGroupKeys['_default_group_'] = true;
                }
            }

            foreach (array_keys($allGroupKeys) as $groupKey) {
                $structure[$groupKey] = [
                    'group_key'       => $groupKey,
                    'group_label'     => $this->getGroupDisplayLabel($groupKey, $groupByField, (array)$params['project_id']),
                    'daily_entries'   => $this->initializeEmptyDateRangeResult($primaryData['dateRange'], true),
                    'work_item_count' => 0
                ];
            }
        } else {
            $structure['default_group'] = [
                'group_key'     => null,
                'group_label'   => null,
                'daily_entries' => $this->initializeEmptyDateRangeResult($primaryData['dateRange'], false)
            ];
        }
        return $structure;
    }

    /**
     * 步骤4：填充报告数据，此过程无IO操作
     * @param  array  $reportStructure
     * @param  array  $primaryData
     * @param  array  $preloadedData
     * @param  array  $params
     */
    private function populateReportData(array &$reportStructure, array $primaryData, array $preloadedData, array $params): void
    {
        $groupByField = $params['group_by_field'] ?? null;
        $isGrouped = ! empty($groupByField);
        $groupWorkItemTracker = []; // 用于计算每个组的唯一工作项数量

        foreach ($primaryData['workHoursRecords'] as $whRecord) {
            $cntId = $whRecord['cnt_id'];
            if ( ! isset($primaryData['workItemsMap'][$cntId])) {
                continue;
            }
            $workItemData = $primaryData['workItemsMap'][$cntId];

            $groupKeys = ['default_group'];
            if ($isGrouped) {
                $keys = $workItemData[$groupByField] ?? null;
                if ($keys === null || $keys === '' || (is_array($keys) && empty($keys))) {
                    $groupKeys = ['_default_group_'];
                } else {
                    $groupKeys = is_array($keys) ? $keys : [$keys];
                }
            }

            foreach ($groupKeys as $groupKey) {
                if ( ! isset($reportStructure[$groupKey])) {
                    continue;
                }

                // 追踪每个组的唯一工作项
                if ($isGrouped) {
                    $groupWorkItemTracker[$groupKey][$cntId] = true;
                }

                $dateStr = $whRecord['work_date'];
                if ( ! isset($reportStructure[$groupKey]['daily_entries'][$dateStr])) {
                    continue;
                }

                $dailyEntryRef = &$reportStructure[$groupKey]['daily_entries'][$dateStr];
                $userIdLogged = $whRecord['create_by'];
                $hours = (float)$whRecord['working_hours'];

                $dailyEntryRef['total_actual_hours_for_day_by_selected_users'] = round($dailyEntryRef['total_actual_hours_for_day_by_selected_users'] + $hours, 2);

                $workItemEntryKey = array_search($cntId, array_column($dailyEntryRef['work_items'], 'cnt_id'));

                if ($workItemEntryKey === false) {
                    $workItemEntry = [
                        'cnt_id' => (int)$workItemData['cnt_id'],
                        'title'  => $workItemData['title'] ?? 'N/A',
                    ];
                    $fieldListForEs = array_unique(
                        array_merge(['cnt_id', 'title', 'priority', 'iteration_id', 'estimated_work_hours', 'status_enum_id', 'handler_uid', 'actual_work_hours', 'cnt_type', 'flow_status_id', 'parent_id', 'root_id', 'subset_cnt_type', 'iteration_process_node_id', 'project_id'],
                            $params['field_list'] ?? [])
                    );
                    foreach ($fieldListForEs as $fieldKey) {
                        if ( ! isset($workItemEntry[$fieldKey]) && isset($workItemData[$fieldKey])) {
                            $workItemEntry[$fieldKey] = $workItemData[$fieldKey];
                        }
                    }
                    $priorityValue = $workItemData['priority'] ?? '';
                    if (is_array($priorityValue)) {
                        $priorityValue = '';
                    }
                    $workItemEntry['priority_name'] = $preloadedData['priorities'][(string)$priorityValue] ?? 'P'.$priorityValue;
                    $workItemEntry['iteration_name'] = $preloadedData['iterations'][$workItemData['iteration_id'] ?? ''] ?? '';
                    $workItemEntry['estimated_work_hours_total'] = (float)($workItemData['estimated_work_hours'] ?? 0);
                    $workItemEntry['actual_work_hours_total'] = (float)($workItemData['actual_work_hours'] ?? 0);
                    $workItemEntry['status_enum_name'] = $this->getPreloadedStatusName($workItemData, $preloadedData['statuses']);
                    $workItemEntry['user_contributions_on_date'] = [];

                    $dailyEntryRef['work_items'][] = $workItemEntry;
                    $workItemEntryKey = count($dailyEntryRef['work_items']) - 1;
                }

                $dailyEntryRef['work_items'][$workItemEntryKey]['user_contributions_on_date'][] = [
                    'user_id'              => (int)$userIdLogged,
                    'user_name'            => $preloadedData['users'][$userIdLogged] ?? '未知用户',
                    'actual_hours_on_date' => $hours,
                ];
            }
        }

        // 循环结束后，更新每个分组的工作项计数
        if ($isGrouped) {
            foreach ($groupWorkItemTracker as $groupKey => $workItems) {
                if (isset($reportStructure[$groupKey])) {
                    $reportStructure[$groupKey]['work_item_count'] = count($workItems);
                }
            }
        }
    }

    /**
     * 步骤5：格式化最终输出
     * @param  array  $reportStructure
     * @param  array  $params
     * @param  array  $dateRange
     * @return array
     */
    private function formatReportOutput(array $reportStructure, array $params, array $dateRange): array
    {
        if (empty($reportStructure)) {
            return ! empty($params['group_by_field']) ? [] : $this->initializeEmptyDateRangeResult($dateRange, false, true);
        }

        $isGrouped = ! empty($params['group_by_field']);
        if ($isGrouped) {
            $output = [];
            $unGroupedData = null;

            if (isset($reportStructure['_default_group_'])) {
                $unGroupedData = $reportStructure['_default_group_'];
                unset($reportStructure['_default_group_']);
            }

            foreach ($reportStructure as $groupData) {
                $output[] = [
                    'group_key'       => $groupData['group_key'],
                    'group_label'     => $groupData['group_label'],
                    'work_item_count' => $groupData['work_item_count'],
                    'days'            => array_values($groupData['daily_entries'])
                ];
            }

            if ($unGroupedData) {
                $output[] = [
                    'group_key'       => $unGroupedData['group_key'],
                    'group_label'     => $unGroupedData['group_label'],
                    'work_item_count' => $unGroupedData['work_item_count'],
                    'days'            => array_values($unGroupedData['daily_entries'])
                ];
            }

            return $output;
        } else {
            return array_values($reportStructure['default_group']['daily_entries']);
        }
    }

    /**
     * 使用预加载的数据获取状态名称
     * @param  array  $workItemData
     * @param  array  $statusesMap
     * @return string
     */
    private function getPreloadedStatusName(array $workItemData, array $statusesMap): string
    {
        $statusEnumId = $workItemData['status_enum_id'] ?? null;
        if ($statusEnumId === null) {
            return '未知状态';
        }

        if (($workItemData['cnt_type'] ?? 0) == WorkItemsModel::CNT_TYPE_TASK) {
            return match ((int)$statusEnumId) {
                1 => "未开始",
                2 => "进行中",
                3 => "已完成",
                default => "未知状态",
            };
        } else {
            return ($statusesMap[$statusEnumId] ?? '未知状态');
        }
    }

    private function initializeEmptyDateRangeResult(array $dateRange, bool $isGroupedContext = false, bool $asValuesForNonGrouped = false): array
    {
        $resultMap = [];
        $dayOfWeekMap = ['Mon' => '周一', 'Tue' => '周二', 'Wed' => '周三', 'Thu' => '周四', 'Fri' => '周五', 'Sat' => '周六', 'Sun' => '周日'];
        foreach ($dateRange as $dateStr) {
            try {
                $dayKey = (new DateTime($dateStr))->format('D');
                $resultMap[$dateStr] = [
                    'date'                                         => $dateStr,
                    'day_of_week'                                  => $dayOfWeekMap[$dayKey] ?? $dayKey,
                    'total_actual_hours_for_day_by_selected_users' => 0.0,
                    'work_items'                                   => [],
                ];
            } catch (\Exception $e) {
                // Ignore invalid date strings
            }
        }
        // 如果是非分组上下文且 $asValuesForNonGrouped 为 true，则返回 array_values。
        // 否则（对于分组上下文，或非分组但需要映射的情况），返回映射。
        return ! $isGroupedContext && $asValuesForNonGrouped ? array_values($resultMap) : $resultMap;
    }

    /**
     * 获取分组的显示标签 (辅助方法, 可能需要根据实际情况调整)
     * @param  mixed        $groupKey
     * @param  string|null  $groupByField
     * @param  array        $projectIds
     * @return string
     */
    private function getGroupDisplayLabel($groupKey, $groupByField, array $projectIds): string
    {
//        $this->esLogic::DEFAULT_ES_GROUP_MISSING_KEY
        if ($groupKey === '_default_group_') { // ES缺失桶键、显式null/空，或0/空字符串表示未分类
            return '--';
        }
        if (empty($groupByField)) {
            return (string)$groupKey;
        }

        // 尝试从 FieldConfig 或 EnumModel 获取标签
        // 这是一个简化的示例, 具体实现可能更复杂, 参考 pageQueryMyJob 中的 Transfer 工具类
        try {
            if (str_ends_with(strtolower($groupByField), '_uid') || $groupByField === 'create_by' || $groupByField === 'update_by') {
                // 如果 projectIds 有多个值，并且同一 user_id 在不同项目中的 user_name 可能不同，则可能需要调整此处的逻辑
                // 目前，如果ORM/DB在单列的where条件中能处理数组，则它将使用所有 project_id，否则可能只使用第一个。
                // 假设给定 user_id 的 user_name 在各项目中是一致的，或者取第一个匹配项是可以接受的。
                $userQuery = ProjectUserModel::whereIn('project_id', $projectIds)->where('user_id', $groupKey);
                $user = $userQuery->find();
                return $user['user_name'] ?? (string)$groupKey;
            }

            // 尝试作为枚举值查找
            // 注意：需要知道 groupByField 对应的 enum_code
            // $enumCode = $this->mapFieldToEnumCode($groupByField); // 假设有这样的映射
            // if ($enumCode) {
            //     $enum = EnumModel::where('enum_code', $enumCode)->where('enum_value', $groupKey)->find();
            //     if ($enum) return $enum['enum_name'];
            // }

            // 尝试从项目特定设置查找，例如类别 type_id
            if ($groupByField === 'type_id') {
                $categorySetting = ProjectCategorySettingsModel::findById($groupKey);
                if ($categorySetting) {
                    return $categorySetting['category_name'];
                }
            }
            if ($groupByField === 'status_enum_id') {
                $enum = EnumModel::findById($groupKey);
                if ($enum) {
                    return $enum['enum_name'];
                }
            }


        } catch (Throwable $e) {
            // 记录错误或忽略，并回退到 groupKey
        }

        return (string)$groupKey; // 回退方案
    }


    private function getPriorityName($priorityValue): string
    {
        if ($priorityValue === null || $priorityValue === '') {
            return '';
        }
        if ( ! is_string($priorityValue)) {
            return '';
        }
        // Assuming priorityValue is the enum_value for 'priority' enum_code
        $priorityModel = EnumModel::where('enum_code', 'priority')
            ->where('enum_value', (string)$priorityValue)
            ->find();
        return $priorityModel['enum_name'] ?? (is_numeric($priorityValue) ? 'P'.$priorityValue : (string)$priorityValue);
    }

    private function getStatusName(array $workItemData): string
    {
        $statusEnumId = $workItemData['status_enum_id'] ?? null;
        if ($statusEnumId === null) {
            return '未知状态';
        }

        if (isset($workItemData['cnt_type']) && ((int)$workItemData['cnt_type'] === WorkItemsModel::CNT_TYPE_TASK)) {
            // 任务状态
            $statusModel = EnumModel::findById((int)$statusEnumId);
            return $statusModel['enum_name'] ?? '未知状态';
        } else {
            // 需求/缺陷状态 (可能涉及 flow_status_id)
            $flowStatusId = $workItemData['flow_status_id'] ?? null;
            if ($flowStatusId) {
                $statusTextModel = FlowStatusTextModel::where('flow_status_id', $flowStatusId)
                    ->where('status_enum_id', $statusEnumId)->find();
                if ($statusTextModel) {
                    return $statusTextModel->status_text_name ?? '未知状态';
                }
            }
            // Fallback if FlowStatusTextModel not found
            $statusModel = EnumModel::findById((int)$statusEnumId);
            return $statusModel['enum_name'] ?? '未知状态';
        }
    }


    /**
     * 获取需求id集合通过迭代id
     * @param $iterationId
     * @return array
     * @throws ClientResponseException
     * @throws ServerResponseException
     * <AUTHOR>
     * @date   2024/12/6 上午11:54
     */
    public static function findDemandIdListByIterationId($iterationId)
    {
        return WorkItemsEsLogic::getInstance()->esSearch([
            ['field_name' => 'iteration_id', 'value' => $iterationId, 'type' => 'term', 'operate_type' => 'equal'],
            ['field_name' => 'cnt_type', 'value' => WorkItemsModel::CNT_TYPE_DEMAND, 'type' => 'term', 'operate_type' => 'equal'],
        ], WorkItemsEsLogic::NOT_PAGE_MAX)->getCollection()->column('cnt_id');
    }

    /**
     * 移出项目指定处理人
     * @param  int  $projectId
     * @param  int  $userId
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws Throwable
     * @throws MissingParameterException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/12/7
     */
    public static function removeProjectUser(int $projectId, int $userId)
    {
        // 查询出工作项下所有处理人
        $cntInfo = self::findUserProjectData($projectId, $userId)->column('handler_uid', 'cnt_id');

        $cntIds = array_keys($cntInfo);

        try {
            DBTransaction::begin();

            WorkItemsModel::where(['cnt_id' => $cntIds, 'is_delete' => BaseModel::DELETE_NOT])
                ->select()
                ->each(function (WorkItemsModel $model) use ($cntInfo, $userId) {
                    if ( ! isset($cntInfo[$model->cnt_id])) {
                        return;
                    }

                    // 移出指定处理人
                    $data['handler_uid'] = array_values(removeValueFromArray($cntInfo[$model->cnt_id], $userId));

                    $model->save($data);
                });
            DBTransaction::commit();
        } catch (Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 替换项目处理人
     * @param  array  $cntIds
     * @param  int    $userId
     * @param  array  $followUpUserIds  需要更新的用户 id
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws Throwable
     * @throws MissingParameterException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     *                                  User Long
     *                                  Date 2024/12/10
     */
    public static function replaceProjectUser(array $cntIds, int $userId, array $followUpUserIds = [])
    {
        // 查询出工作项下所有处理人
        $cntInfo = self::findUserProjectDataByCntIds($cntIds)->column('handler_uid', 'cnt_id');

        try {
            DBTransaction::begin();

            WorkItemsModel::where(['cnt_id' => $cntIds, 'is_delete' => BaseModel::DELETE_NOT])
                ->select()
                ->each(function (WorkItemsModel $model) use ($cntInfo, $userId, $followUpUserIds) {
                    // 查无数据，并且不需要更新，直接返回
                    if ( ! isset($cntInfo[$model->cnt_id]) && ( ! $userId && ! $followUpUserIds)) {
                        return;
                    }

                    // 移出指定处理人
                    $data['handler_uid'] = array_values(removeValueFromArray($cntInfo[$model->cnt_id] ?? [], $userId));

                    // 增加跟进人
                    if ($followUpUserIds) {
                        $data['handler_uid'] = array_values(array_unique(array_merge($data['handler_uid'], $followUpUserIds)));
                    }

                    $model->save($data);
                });
            DBTransaction::commit();
        } catch (Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 获取项目指定处理人的工作项集合
     * @param  int  $projectId
     * @param  int  $userId
     * @return Collection|\think\model\Collection
     * @throws ClientResponseException
     * @throws ServerResponseException
     * User Long
     * Date 2024/12/9
     */
    public static function findUserProjectData(int $projectId, int $userId)
    {
        return Es::getInstance()
            ->setQueryParam(['field_name' => 'project_id', 'value' => $projectId, 'type' => 'term', 'operate_type' => 'equal'])
            ->setQueryParam(['field_name' => 'handler_uid', 'value' => [$userId], 'type' => 'selector'])
            ->setQueryParam(['field_name' => 'isEnd', 'value' => false, 'type' => 'term', 'operate_type' => 'equal'])
            ->setPage(WorkItemsEsLogic::NOT_PAGE_MAX)
            ->getPaginator()
            ->getCollection();
    }

    /**
     * 获取项目指定工作项数据
     * @param  array  $cntIds
     * @return Collection|\think\model\Collection
     * @throws ClientResponseException
     * @throws ServerResponseException
     * User Long
     * Date 2024/12/10\
     */
    public static function findUserProjectDataByCntIds(array $cntIds)
    {
        return Es::getInstance()
            ->setQueryParam(['field_name' => 'cnt_id', 'value' => $cntIds, 'type' => 'selector'])
            ->setQueryParam(['field_name' => 'isEnd', 'value' => false, 'type' => 'term', 'operate_type' => 'equal'])
            ->setPage(WorkItemsEsLogic::NOT_PAGE_MAX)
            ->getPaginator()
            ->getCollection();
    }

    /**
     * 查询迭代工作项数据
     * @param  int  $iterationId
     * @return Collection|\think\model\Collection
     * @throws ClientResponseException
     * @throws ServerResponseException
     * User Long
     * Date 2024/12/14
     */
    public static function findIterationDataByIterationId(int $iterationId)
    {
        return Es::getInstance()
            ->setQueryParam(['field_name' => 'iteration_id', 'value' => $iterationId, 'type' => 'term', 'operate_type' => 'equal'])
            ->setPage(WorkItemsEsLogic::NOT_PAGE_MAX)
            ->getPaginator()
            ->getCollection();
    }

    /**
     * 获取迭代平均进度
     * @param $iterationId
     * @return float|int
     * <AUTHOR>
     * @date   2024/12/16 上午10:13
     */
    public function completeProgress($iterationId)
    {
        $where = [
            ['field_name' => 'iteration_id', 'value' => $iterationId, 'type' => 'term', 'operate_type' => 'equal']
        ];
        $result = $this->esLogic->completeProgress($where);

        $all = ($result[0] ?? 0) + ($result[1] ?? 0);
        $isEnd = ($result[1] ?? 0);
        if ( ! $all) {
            return 0;
        }

        return bcdiv((string)$isEnd, (string)$all, 2) * 100;
    }

    /**
     * 批量删除工作项
     * @param  array  $cntIds  工作项ID数组
     * @return array 删除结果统计
     * @throws Throwable
     * <AUTHOR>
     * @date   2024/1/15
     */
    public function batchDelete(array $cntIds)
    {
        if (empty($cntIds)) {
            throw new ParamsException("工作项ID不能为空");
        }

        $successCount = 0;
        $failCount = 0;
        $result = [
            'failed'  => [
                'demand' => [],
                'task'   => [],
                'flaw'   => []
            ],
            'success' => [
                'demand' => [],
                'task'   => [],
                'flaw'   => []
            ]
        ];

        DBTransaction::begin();
        try {
            WorkItemsModel::$autoSave = false;
            TestPlanModel::$autoSave = false;
            TestCaseModel::$autoSave = false;
            TestPlanLogic::$isTriggerStatistics = false;

            foreach ($cntIds as $id) {
                $model = WorkItemsModel::findById($id);
                if ( ! $model || $model->extends['is_delete'] === BaseModel::DELETE_YES) {
                    $failCount++;
                    $result['failed'][match ($model?->extends['cnt_type']) {
                        WorkItemsModel::CNT_TYPE_DEMAND => 'demand',
                        WorkItemsModel::CNT_TYPE_TASK => 'task',
                        WorkItemsModel::CNT_TYPE_FLAW => 'flaw',
                        default => 'demand'
                    }][]
                        = $id;
                    continue;
                }

                //获取树
                $tree = $this->getTree($model->extends['root_id']);

                //删除所有后代
                $this->recursiveChildren($tree, $model->cnt_id, function (WorkItemsModel $model) {
                    //缺陷不删除,只是解除关联
                    if ($model->isFlaw()) {
                        $model->save(['parent_id' => 0]);
                    } else {
                        $model->save(['is_delete' => BaseModel::DELETE_YES]);
                    }
                });

                $model->save(['is_delete' => BaseModel::DELETE_YES]);

                //更新父级的工时
                $model->isTask() && $this->updateWorkHours($model->extends['parent_id']);

                $successCount++;
                $result['success'][match ($model->extends['cnt_type']) {
                    WorkItemsModel::CNT_TYPE_DEMAND => 'demand',
                    WorkItemsModel::CNT_TYPE_TASK => 'task',
                    WorkItemsModel::CNT_TYPE_FLAW => 'flaw',
                    default => 'demand'
                }][]
                    = $id;
            }

            TestPlanLogic::$isTriggerStatistics = true;
            WorkItemsModel::actualSaving();
            TestPlanModel::actualSaving();
            TestCaseModel::actualSaving();
            DBTransaction::commit();

            $msg = sprintf("批量删除成功%d条，失败%d条。", $successCount, $failCount);

            return [
                'msg'      => '删除成功',
                'data'     => $result,
                'msg_type' => 1
            ];
        } catch (Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }


    /**
     * 是否含子任务，子需求
     * @param $ids
     * @return bool
     * @throws ClientResponseException
     * @throws ServerResponseException
     */
    public function hasChild($ids)
    {
        return ! $this->esLogic->esSearch([
            ['field_name' => 'parent_id', 'value' => $ids, 'type' => 'selector'],
            ['field_name' => 'cnt_type', 'value' => [WorkItemsModel::CNT_TYPE_DEMAND, WorkItemsModel::CNT_TYPE_TASK], 'type' => 'selector'],
        ])->getCollection()->isEmpty();
    }

    /**
     * 批量状态流转
     * @param  array  $params
     * @throws Throwable
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @throws MissingParameterException
     */
    public function batchStatusTransfer(array $params)
    {
        $cntIds = $params['cnt_ids'];
        $targetStatus = $params['status_enum_id'];
        $updateEmptyOnly = $params['update_empty_only'] ?? false;

        // 提取额外的字段，用于传递给statusTransfer
        $additionalFields = array_diff_key($params, array_flip(['cnt_ids', 'status_enum_id', 'update_empty_only']));

        // 获取工作项列表
        $items = WorkItemsModel::where(['cnt_id' => $cntIds, 'is_delete' => BaseModel::DELETE_NOT])
            ->select();

        if ($items->isEmpty()) {
            throw new NotFoundException('未找到相关工作项');
        }

        // 按工作项类型分组
        $groupedItems = [];
        foreach ($items as $item) {
            $groupedItems[$item->cnt_type][] = $item;
        }

        DBTransaction::begin();
        try {
            // 处理需求和缺陷
            if (
                isset($groupedItems[WorkItemsModel::CNT_TYPE_DEMAND])
                || isset($groupedItems[WorkItemsModel::CNT_TYPE_FLAW])
            ) {
                $this->batchWorkflowStatusTransfer(
                    array_merge(
                        $groupedItems[WorkItemsModel::CNT_TYPE_DEMAND] ?? [],
                        $groupedItems[WorkItemsModel::CNT_TYPE_FLAW] ?? []
                    ),
                    $targetStatus,
                    $updateEmptyOnly,
                    $additionalFields
                );
            }

            // 处理任务
            if (isset($groupedItems[WorkItemsModel::CNT_TYPE_TASK])) {
                $this->batchTaskStatusTransfer(
                    $groupedItems[WorkItemsModel::CNT_TYPE_TASK],
                    $targetStatus,
                    $additionalFields,
                    $updateEmptyOnly
                );
            }

            DBTransaction::commit();
        } catch (Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 批量工作流状态流转(需求/缺陷)
     * @param  array  $items
     * @param  int    $targetStatus
     * @param  bool   $updateEmptyOnly
     * @param  array  $additionalFields
     * @throws Throwable
     */
    private function batchWorkflowStatusTransfer(array $items, $targetStatus, bool $updateEmptyOnly, array $additionalFields = [])
    {
        foreach ($items as $item) {
            // 获取可流转状态
            $availableStatus = FlowStatusTextLogic::getTargetStatusEnum(
                $item->extends['flow_status_id'],
                $item->extends['status_enum_id']
            );

            // 验证目标状态是否可流转
            $canTransfer = false;
            foreach ($availableStatus['flow_status_transfer'] as $status) {
                if ($status['target_status_enum_id'] == $targetStatus) {
                    $canTransfer = true;
                    break;
                }
            }

            if ( ! $canTransfer) {
                throw new ParamsException("工作项[{$item->cnt_id}]不能流转到目标状态");
            }

            // 构建状态流转参数，状态字段始终更新
            $transferParams = [
                'cnt_id'         => $item->cnt_id,
                'status_enum_id' => $targetStatus
            ];

            // 处理额外字段，如果设置了仅更新空字段，则只更新空字段
            foreach ($additionalFields as $field => $value) {
                if ($field === 'remark' || $field === 'content') {
                    $transferParams[$field] = $value;
                    continue;
                }
                if ($updateEmptyOnly) {
                    // 处理人字段的特殊处理
                    if ($item->extends[$field] ?? null) {
                        continue;
                    }
                }
                $transferParams[$field] = $value;
            }
            // 执行状态流转
            $this->statusTransfer($transferParams);
        }
    }

    /**
     * 批量任务状态流转
     * @param  array  $items
     * @param  int    $targetStatus
     * @param  array  $additionalFields
     * @param  bool   $updateEmptyOnly
     * @throws Throwable
     */
    private function batchTaskStatusTransfer(array $items, $targetStatus, array $additionalFields = [], bool $updateEmptyOnly = false)
    {
        // 验证任务状态值
        $validTaskStatus = [
            EnumLogic::findEnumValue('notStarted'),  // 未开始
            EnumLogic::findEnumValue('inProgress'),  // 进行中
            EnumLogic::findEnumValue('completed')     // 已完成
        ];

        if ( ! in_array($targetStatus, $validTaskStatus)) {
            throw new ParamsException('无效的任务状态');
        }

        // 批量更新任务状态，状态字段始终更新
        $this->setItemsStatusId(array_column($items, 'cnt_id'), (string)$targetStatus);
    }


    /**
     * 批量更新工作项类别
     * @param  array  $cntIds            工作项ID数组
     * @param  int    $targetCategoryId  目标类别ID
     * @return bool   更新结果，成功返回true，失败抛出异常
     * @throws Throwable
     */
    public function batchUpdateCategory(array $cntIds, int $targetCategoryId, $statusEnumId = null)
    {
        if (empty($cntIds)) {
            throw new ParamsException("工作项ID不能为空");
        }

        if ($targetCategoryId <= 0) {
            throw new ParamsException("目标类别ID无效");
        }

        // 验证目标类别是否存在
        $category = ProjectCategorySettingsModel::findById($targetCategoryId);
        if ( ! $category) {
            throw new ParamsException("目标类别不存在");
        }

        try {
            Db::startTrans();

            $workItems = WorkItemsModel::where([
                'cnt_id'    => $cntIds,
                'is_delete' => BaseModel::DELETE_NOT
            ])->select();

            if ($workItems->isEmpty()) {
                throw new ParamsException("未找到有效的工作项");
            }

            $typeSettings = [];
            if ( ! $workItems->first()->isTask()) {
                // 获取流程信息（仅需获取一次）任务用不到
                $typeSettings = ProjectCategorySettingsLogic::getDefaultFlowStatusByCategorySettingsId($targetCategoryId);
            }

            foreach ($workItems as $workItem) {
                // 如果类别已经相同，则跳过
                if ((int)$workItem->extends['type_id'] == $targetCategoryId) {
                    continue;
                }

                $updateData = ['type_id' => $targetCategoryId, 'status_enum_id' => $statusEnumId];

                // 对于需求和缺陷类型，更新流程ID
                if ($workItem->isDemand() || $workItem->isFlaw()) {
                    $updateData['flow_status_id'] = $typeSettings['flowStatusId'];
                    $updateData['status_enum_id'] = $statusEnumId ?? $typeSettings['statusEnumId'];
                }

                $this->update(array_merge($updateData, ['cnt_id' => $workItem->cnt_id]));
            }

            Db::commit();
            return true;
        } catch (Throwable $e) {
            Db::rollback();
            throw $e;
        }
    }


    /**
     * 批量更新工作项
     * @param  array  $params  包含cnt_ids和update_fields的数组
     * @return array 更新结果
     * @throws Throwable
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function batchUpdate(array $params)
    {
        $cntIds = $params['cnt_ids'];
        $cntType = $params['cnt_type'] ?? 0;

        // 只处理field_list格式的参数
        if ( ! isset($params['field_list']) || ! is_array($params['field_list'])) {
            throw new ParamsException("field_list参数不能为空");
        }

        // 获取可编辑字段列表
        $allowedFields = $this->getAllowedFieldsByCntType($cntType);
        if (empty($allowedFields)) {
            throw new ParamsException("未找到可编辑字段配置");
        }

        $updateFields = [];
        foreach ($params['field_list'] as $field) {
            if (isset($field['field_name'])) {
                // 验证字段是否在允许的范围内
                if ( ! in_array($field['field_name'], $allowedFields)) {
                    continue; // 跳过不允许编辑的字段
                }

                // 处理vlaue拼写错误，同时兼容value正确拼写
                $value = $field['value'] ?? $field['vlaue'] ?? null;
                $updateFields[$field['field_name']] = $value;
            }
        }

        if (empty($cntIds)) {
            throw new ParamsException("工作项ID不能为空");
        }

        if (empty($updateFields)) {
            throw new ParamsException("更新字段不能为空");
        }

        $result = [
            'demand' => [],
            'task'   => [],
            'flaw'   => []
        ];
        $hasSuccess = false;

        DBTransaction::begin();
        try {
            WorkItemsModel::$autoSave = false;
            foreach ($cntIds as $id) {
                $model = WorkItemsModel::findById($id);
                if ( ! $model || $model->extends['is_delete'] === BaseModel::DELETE_YES) {
                    continue;
                }

                // 构建更新数据
                $updateData = [];
                foreach ($updateFields as $field => $value) {
                    // 特殊字段处理
                    switch ($field) {
                    case 'iteration_id':
                        // 检查是否可以设置迭代
                        if ( ! $this->isSetIteration($model)) {
                            continue 2; // 跳过这个字段的更新
                        }
                        $updateData[$field] = $value;
                        break;
                    default:
                        $updateData[$field] = $value;
                        break;
                    }
                }

                // 执行更新
                if ( ! empty($updateData)) {
//                     如果更新了工时相关字段
                    if (isset($updateData['estimated_work_hours']) || isset($updateData['actual_work_hours']) || isset($updateData['remaining_work'])) {
                        $key = array_key_first($updateData);
                        $type = match ($key) {
                            'estimated_work_hours' => 1,
                            'actual_work_hours' => 2,
                            'remaining_work' => 3,
                        };
                        (new WorkHoursLogic())->create([
                            'cnt_id'        => $model->cnt_id,
                            'type'          => $type,
                            'working_hours' => $updateData[$key],
                            'work_date'     => date("Y-m-d"),
                        ]);

                    } else {
                        $this->update(array_merge($updateData, ['cnt_id' => $model->cnt_id]));
                    }
                }

                $hasSuccess = true;
                $result[match ($model->extends['cnt_type']) {
                    WorkItemsModel::CNT_TYPE_DEMAND => 'demand',
                    WorkItemsModel::CNT_TYPE_TASK => 'task',
                    WorkItemsModel::CNT_TYPE_FLAW => 'flaw',
                    default => 'demand'
                }][]
                    = $id;
            }

            WorkItemsModel::actualSaving();
            DBTransaction::commit();

            $msg = $hasSuccess ? "批量更新成功" : "批量更新失败";

            return [
                'msg'      => $msg,
                'data'     => [
                    'result'  => $result,
                    'success' => $hasSuccess
                ],
                'msg_type' => $hasSuccess ? 1 : 0
            ];
        } catch (Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 根据工作项类型获取允许编辑的字段列表
     * @param  int  $cntType  工作项类型 1-需求 2-任务 3-缺陷
     * @return array 允许编辑的字段名称数组
     */
    private function getAllowedFieldsByCntType(int $cntType): array
    {
        $subKey = match ($cntType) {
            WorkItemsModel::CNT_TYPE_DEMAND => 'demand_edit',
            WorkItemsModel::CNT_TYPE_TASK => 'task_edit',
            WorkItemsModel::CNT_TYPE_FLAW => 'flaw_edit',
            default => ''
        };

        if (empty($subKey)) {
            return [];
        }

        $fieldList = FieldSubsetModel::getByKey($subKey);
        if (empty($fieldList)) {
            return [];
        }

        // 提取字段名称
        $allowedFields = [];
        foreach ($fieldList['field_list'] as $field) {
            if (isset($field['field_name'])) {
                $allowedFields[] = $field['field_name'];
            }
        }

        return $allowedFields;
    }

    /**
     * 格式化排序参数
     * @param  mixed  $order  排序参数，支持字符串（如 "field desc, field2 asc"）或数组格式
     * @return array Elasticsearch排序格式的数组
     * <AUTHOR>
     * @date   2024/7/23
     */
    private function formatOrderParams($order)
    {
        $result = [];

        // 空值处理，使用默认的sort_order
        if (empty($order)) {
            return ['sort_order' => ['order' => 'desc']];
        }

        // 字符串格式: "field desc, field2 asc"
        if (is_string($order)) {
            $orderItems = explode(',', $order);
            foreach ($orderItems as $item) {
                $item = trim($item);
                if (empty($item)) {
                    continue;
                }

                $parts = explode(' ', $item, 2);
                $field = trim($parts[0]);
                $direction = isset($parts[1]) ? strtolower(trim($parts[1])) : 'desc';

                if ($direction !== 'asc' && $direction !== 'desc') {
                    $direction = 'desc'; // 默认降序
                }

                $result[$field] = ['order' => $direction];
            }
            return $result;
        }

        // 数组格式1: ['field' => 'desc', 'field2' => 'asc']
        if (is_array($order) && ! isset($order[0])) {
            foreach ($order as $field => $direction) {
                $direction = strtolower(trim($direction));
                if ($direction !== 'asc' && $direction !== 'desc') {
                    $direction = 'desc'; // 默认降序
                }
                $result[$field] = ['order' => $direction];
            }
            return $result;
        }

        // 数组格式2: [['field' => 'field1', 'direction' => 'desc'], [...]]
        if (is_array($order) && isset($order[0]) && is_array($order[0])) {
            foreach ($order as $item) {
                if ( ! isset($item['field'])) {
                    continue;
                }

                $field = $item['field'];
                $direction = isset($item['direction']) ? strtolower(trim($item['direction'])) : 'desc';

                if ($direction !== 'asc' && $direction !== 'desc') {
                    $direction = 'desc'; // 默认降序
                }

                $result[$field] = ['order' => $direction];
            }
            return $result;
        }

        // 如果结果为空，默认使用sort_order
        if (empty($result)) {
            return ['sort_order' => ['order' => 'desc']];
        }

        return $result;
    }

    /**
     * bug定时任务
     * @return null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function BugScheduledTasks()
    {
        return BugStatistics::statistics();
    }


    /**
     * 拖拽排序时更新项目的排序顺序
     * @param  array  $cntIdList  被移动项目的ID
     * @param  int    $prevCntId  新位置前一个项目的ID（如果是第一个则为null）
     * @param  int    $nextCntId  新位置后一个项目的ID（如果是最后一个则为null）
     * @return bool 成功状态
     * @throws Throwable
     */
    public function updateItemSortOrder($cntIdList, $prevCntId = null, $nextCntId = null)
    {

        if (count($cntIdList) > 1) {
            //函数设计有问题，同时排序多个会导致排序值都一样，这样当有其他工作项插入这些相同排序值的中间就无法做到了，因为一样，所以做后的新插入的排序值运算结果也会是一样
            throw new Exception('不可同时排序多个');
        }

        $modelList = WorkItemsModel::findListById($cntIdList);
        if ($modelList->isEmpty()) {
            throw new NotFoundException("工作项不存在");
        }
        $modelList->each(function (WorkItemsModel $modle) {
            if ( ! $modle->isDemand()) {
                throw new Exception("只可拖动需求");
            }
        });

        if ( ! ($prevCntId) && ! ($nextCntId)) {
            throw new Exception('两个位置参数不可都为空');
        }
        if ($prevCntId == $nextCntId) {
            throw new Exception('两个位置参数不可相同');
        }

        //查询排序值最大的+1
        $maxOrder = $this->esLogic->esSearch([], 1, 1, [], [
            'sort_order' => [
                'order' => 'desc'
            ]
        ])->getCollection()->first();
        $maxOrder = $maxOrder ? $maxOrder['sort_order'] + 1 : PHP_FLOAT_MAX;

        // 边界情况的默认值
        $prevSortOrder = $maxOrder;
        $nextSortOrder = 0;

        // 获取前一个项目的sort_order（如果存在）
        if ($prevCntId) {
            $prevItem = $this->esLogic->esSearch([
                ['field_name' => 'cnt_id', 'value' => $prevCntId, 'type' => 'term', 'operate_type' => 'equal']
            ])->getCollection()->first();

            if ($prevItem) {
                $prevSortOrder = $prevItem['sort_order'] ?? $maxOrder;
            }
        } else {
            //没有的话查询比next大的第一个
            $nextItem = $this->esLogic->esSearch([
                ['field_name' => 'cnt_id', 'value' => $nextCntId, 'type' => 'term', 'operate_type' => 'equal']
            ])->getCollection()->first();

            $nextItem = $this->esLogic->esSearch([
                ['field_name' => 'sort_order', 'value' => [$nextItem['sort_order'], null], 'type' => 'term', 'operate_type' => 'between']
            ], 2, 1, [], [
                'sort_order' => [
                    'order' => 'asc'
                ]
            ])->getCollection()->last();

            if ($nextItem && $nextItem['cnt_id'] != $nextCntId) {
                $prevSortOrder = $nextItem['sort_order'] ?? $maxOrder;
            } else {
                $prevSortOrder += 1;
            }
        }

        // 获取后一个项目的sort_order（如果存在）
        if ($nextCntId) {
            $nextItem = $this->esLogic->esSearch([
                ['field_name' => 'cnt_id', 'value' => $nextCntId, 'type' => 'term', 'operate_type' => 'equal']
            ])->getCollection()->first();

            if ($nextItem) {
                $nextSortOrder = $nextItem['sort_order'] ?? 0;
            }
        } else {
            //没有的话查询比prev小的第一个
            $prevItem = $this->esLogic->esSearch([
                ['field_name' => 'cnt_id', 'value' => $prevCntId, 'type' => 'term', 'operate_type' => 'equal']
            ])->getCollection()->first();

            $prevItem = $this->esLogic->esSearch([
                ['field_name' => 'sort_order', 'value' => [null, $prevItem['sort_order']], 'type' => 'term', 'operate_type' => 'between']
            ], 2, 1, [], [
                'sort_order' => [
                    'order' => 'desc'
                ]
            ])->getCollection()->last();

            if ($prevItem && $prevItem['cnt_id'] != $prevCntId) {
                $nextSortOrder = $prevItem['sort_order'] ?? 0;
            } else {
                $nextSortOrder -= 1;

            }
        }

        //前值小于后值，这表明参照项不处于正常排序，找一个同类重新调一下
        if ($prevSortOrder < $nextSortOrder) {
            if ($prevItem['cnt_type'] == $modelList->first()['cnt_type']) {
                return $this->updateItemSortOrder([$modelList->first()['cnt_id']], $prevItem['cnt_id'], null);
            } else {
                return $this->updateItemSortOrder([$modelList->first()['cnt_id']], null, $nextItem['cnt_id']);
            }
        } elseif ($prevSortOrder == $nextSortOrder) {
            throw new Exception('参照项排序值相同，无法排序');
        }


        // 计算新的sort_order（前后两个值的中点）
        $newSortOrder = $prevSortOrder + ($nextSortOrder - $prevSortOrder) / 2;

        // 更新项目的sort_order
        DBTransaction::begin();
        try {
            $modelList->each(function ($model) use ($newSortOrder) {
                $model->save(['sort_order' => $newSortOrder]);
            });
            DBTransaction::commit();
            return true;
        } catch (Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }
    }

    /**
     * 导出用户工时按日期范围到Excel
     * @param  array  $params  查询参数，应包含:
     *                         - project_id (int|array, required): 项目ID或项目ID数组
     *                         - start_date (string, required, 'YYYY-MM-DD'): 开始日期
     *                         - end_date (string, required, 'YYYY-MM-DD'): 结束日期
     *                         - user_ids (array, required): 用户ID数组
     *                         - ?searchParams (array): 传递给 queryUserWorkHoursByDateRange 的额外ES查询参数
     *                         - ?order (array|string): 传递给 queryUserWorkHoursByDateRange 的排序参数
     *                         - ?group_by_field (string): 传递给 queryUserWorkHoursByDateRange 的分组字段
     *                         - ?fileName (string): 导出的Excel文件名 (不含扩展名)
     * @return \think\Response
     * @throws ParamsException
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function exportUserWorkHoursByDateRangeToExcel(array $params): \think\Response
    {
        validate(WorkItemsValidate::class)->scene('queryUserWorkHours')->check($params);

        $projectIdsInput = $params['project_id'];
        $projectIds = is_array($projectIdsInput) ? $projectIdsInput : [$projectIdsInput];
        $projectIds = array_map('intval', array_filter($projectIds));
        if (empty($projectIds)) {
            throw new ParamsException("项目ID project_id 不能为空或包含无效值");
        }

        $startDate = $params['start_date'];
        $endDate = $params['end_date'];
        $baseFileName = $params['fileName'] ?? '用户工时统计导出';
        $currentDateTime = date('Ymd_His');
        $fileName = "{$baseFileName}_{$currentDateTime}";
        $userIdsForQuery = $params['user_ids'];

        // 获取数据，确保 group_by_field 为 null 以便我们自己处理所有聚合和格式化
        $queryDataParams = $params;
        $queryDataParams['project_id'] = $projectIds;
        $queryDataParams['group_by_field'] = null; // 获取最原始的每日每用户每工作项工时数据

        $queryDataParams['field_list'] = [
            "cnt_id",
            "estimated_work_hours",
            "actual_work_hours",
            "project_id",
            "handler_uid"
        ];
        // $workHoursDataRaw 的结构: [ 'date_YYYY-MM-DD' => ['date' => ..., 'total_actual_hours_for_day_by_selected_users' => ..., 'work_items' => [...] ] ]
        $workHoursDataRaw = $this->queryUserWorkHoursByDateRange($queryDataParams);

        $customizeDayLogic = new CustomizeTheWorkingDayLogic();
        $projectDetails = [];
        $standardHoursForSheet1 = null;         // 用于人员工时统计表的标准工时

        foreach ($projectIds as $pid) {
            $config = $customizeDayLogic->getByProjectId(-1); // Use the new method
            $projectModel = ProjectModel::find($pid);
            $projectName = $projectModel ? $projectModel['project_name'] : "项目{$pid}";
            $projectDetails[$pid] = [
                'name'           => $projectName,
                'standard_hours' => $config['work_hours'],
            ];
        }
        if ( ! empty($projectIds)) {
            $firstProjectId = reset($projectIds);                                         // 获取数组的第一个元素值
            // $projectDetails[$firstProjectId]['standard_hours'] 应总是有值（CustomizeTheWorkingDayLogic有默认7）
            $standardHoursForSheet1 = $projectDetails[$firstProjectId]['standard_hours']; // 使用第一个项目的标准工时，或默认7
        }


        $spreadsheet = new Spreadsheet();
        $spreadsheet->removeSheetByIndex(0);

        $allDates = $this->getDataList($startDate, $endDate);

        // 数据重组: [userId => ['name' => userName, 'org' => orgName, 'daily_hours' => [date => hours], 'total_hours' => total, 'days_with_hours' => count]]
        $userDataForSheet1 = [];
        // 数据重组: [projectId => [userId => ['name' => userName, 'org' => orgName, 'daily_hours' => [date => hours], 'total_hours' => total, 'days_with_hours' => count]]]
        $projectUserDataForSheets = [];
        // 项目总工时: [projectId => totalHours]
        $projectTotalHours = array_fill_keys($projectIds, 0.0);
        // 项目参与人数: [projectId => [userId => true]]
        $projectActiveUsers = array_fill_keys($projectIds, []);

        // 预先获取所有相关用户的基本信息（姓名、组织等）
        $allRelevantUserIds = $userIdsForQuery; // 开始时是查询参数中的用户
        foreach ($workHoursDataRaw as $dayData) {
            foreach ($dayData['work_items'] as $workItem) {
                foreach ($workItem['user_contributions_on_date'] as $contribution) {
                    $allRelevantUserIds[] = (int)$contribution['user_id'];
                }
            }
        }
        $allRelevantUserIds = array_values(array_unique($allRelevantUserIds));
        $usersFullDetails = [];
        if ( ! empty($allRelevantUserIds)) {
            // 假设 ProjectUserModel 有 department 字段，如果实际字段不同，需要修改此处
            $usersFullDetails = ProjectUserModel::whereIn('user_id', $allRelevantUserIds)
                ->field(['user_id', 'user_name']) // 'department' 是假设的组织字段
                ->select()
                ->column(null, 'user_id');
        }


        // 初始化 userDataForSheet1 和 projectUserDataForSheets
        // 同时记录每个用户在哪些项目中有工时 (用于Sheet1的所属项目列)
        $userProjectParticipation = [];

        foreach ($userIdsForQuery as $uid) {
            $userName = isset($usersFullDetails[$uid]['user_name']) && ! empty($usersFullDetails[$uid]['user_name']) ? $usersFullDetails[$uid]['user_name'] : "用户{$uid}";
            $userDataForSheet1[$uid] = [
                'name'            => $userName,
                'project_info'    => '', // 将用于显示所属项目信息
                'daily_hours'     => array_fill_keys($allDates, 0.0),
                'total_hours'     => 0.0,
                'days_with_hours' => 0
            ];
            $userProjectParticipation[$uid] = []; // 初始化项目参与记录
        }

        foreach ($projectIds as $pid) {
            $projectUserDataForSheets[$pid] = [];
        }

        foreach ($workHoursDataRaw as $dayData) {
            $currentDate = $dayData['date'];
            foreach ($dayData['work_items'] as $workItem) {
                $pid = (int)($workItem['project_id'] ?? 0);
                if ( ! in_array($pid, $projectIds)) {
                    continue;
                }

                $projectNameForWorkItem = $projectDetails[$pid]['name'] ?? "项目{$pid}";

                foreach ($workItem['user_contributions_on_date'] as $contribution) {
                    $userId = (int)$contribution['user_id'];
                    $hours = (float)$contribution['actual_hours_on_date'];

                    if ($hours > 0) {
                        // 为 Sheet 1 ("人员工时统计") 聚合数据
                        if (isset($userDataForSheet1[$userId])) {
                            $userDataForSheet1[$userId]['daily_hours'][$currentDate] += $hours;
                            $userDataForSheet1[$userId]['total_hours'] += $hours;
                            $userProjectParticipation[$userId][$pid] = $projectNameForWorkItem; // 记录用户参与的项目
                        }

                        // 为项目详情 Sheet 聚合数据
                        if ( ! isset($projectUserDataForSheets[$pid][$userId])) {
                            $userNameProject = isset($usersFullDetails[$userId]['user_name']) && ! empty($usersFullDetails[$userId]['user_name']) ? $usersFullDetails[$userId]['user_name'] : "用户{$userId}";
                            $projectUserDataForSheets[$pid][$userId] = [
                                'name'            => $userNameProject,
                                'project_name'    => $projectNameForWorkItem, // 所属项目直接用当前项目名
                                'daily_hours'     => array_fill_keys($allDates, 0.0),
                                'total_hours'     => 0.0,
                                'days_with_hours' => 0
                            ];
                        }
                        $projectUserDataForSheets[$pid][$userId]['daily_hours'][$currentDate] += $hours;
                        $projectUserDataForSheets[$pid][$userId]['total_hours'] += $hours;

                        $projectTotalHours[$pid] += $hours;
                        $projectActiveUsers[$pid][$userId] = true;
                    }
                }
            }
        }

        // 计算 days_with_hours 和 Sheet1 的 project_info
        foreach ($userDataForSheet1 as $uid => &$uData) {
            foreach ($uData['daily_hours'] as $dHours) {
                if ($dHours > 0) {
                    $uData['days_with_hours']++;
                }
            }

            $participatedProjects = $userProjectParticipation[$uid] ?? [];
            if (count($participatedProjects) === 1) {
                $uData['project_info'] = reset($participatedProjects); // 获取唯一的项目名称
            } elseif (count($participatedProjects) > 1) {
                $uData['project_info'] = implode('、', array_values($participatedProjects)); // 使用 '、' 分隔多个项目名称
            } else {
                $uData['project_info'] = '-'; // 如果没有参与任何项目（理论上total_hours为0会被过滤）
            }
        }
        unset($uData);
        foreach ($projectUserDataForSheets as $pid => &$pUsers) {
            foreach ($pUsers as $uid => &$uData) {
                foreach ($uData['daily_hours'] as $dHours) {
                    if ($dHours > 0) {
                        $uData['days_with_hours']++;
                    }
                }
            }
            unset($uData);
        }
        unset($pUsers);

        // 过滤掉没有工时的用户 (Sheet 1)
        $userDataForSheet1 = array_filter($userDataForSheet1, function ($userData) {
            return $userData['total_hours'] > 0.0;
        });
        // 为确保一致的输出顺序，在过滤后对 $userDataForSheet1 按用户ID排序
        uksort($userDataForSheet1, fn($a, $b) => (int)$a <=> (int)$b);

        // --- Sheet 1: 人员工时统计 ---
        $personSheet = $spreadsheet->createSheet();
        $personSheet->setTitle('人员工时统计');
        $headerPerson = ['人员', '所属项目']; // 表头修改
        foreach ($allDates as $date) {
            $headerPerson[] = $date;
        }
        $headerPerson[] = '工时总计';
        $headerPerson[] = '平均工时';
        $personSheet->fromArray($headerPerson, null, 'A1');

        $row = 2;
        foreach ($userDataForSheet1 as $userId => $data) {
            $col = 1;
            $personSheet->setCellValue(Coordinate::stringFromColumnIndex($col++).$row, $data['name']);
            $personSheet->setCellValue(Coordinate::stringFromColumnIndex($col++).$row, $data['project_info']); // 使用新的 project_info
            foreach ($allDates as $date) {
                $hours = $data['daily_hours'][$date] ?? 0.0;
                $cellCoordinate = Coordinate::stringFromColumnIndex($col).$row;
                $personSheet->setCellValue($cellCoordinate, $hours > 0 ? round($hours, 2) : 0);

                // 使用全局为Sheet1确定的standardHoursForSheet1进行颜色标记
                if ($hours > 0 && $standardHoursForSheet1 !== null) {
                    if ($hours > $standardHoursForSheet1) {
                        $personSheet->getStyle($cellCoordinate)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setARGB('FF99CC33'); // New Green
                    } elseif ($hours < $standardHoursForSheet1) {
                        $personSheet->getStyle($cellCoordinate)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setARGB('FFFF0000'); // New Red
                    }
                }
                $col++;
            }
            $personSheet->setCellValue(Coordinate::stringFromColumnIndex($col++).$row, $data['total_hours'] > 0 ? round($data['total_hours'], 2) : 0);
            $avgHours = count($allDates) > 0 ? round($data['total_hours'] / count($allDates), 2) : 0;
            $personSheet->setCellValue(Coordinate::stringFromColumnIndex($col++).$row, $avgHours > 0 ? $avgHours : 0);
            $row++;
        }

        // --- Sheet 2: 项目工时统计 ---
        $projectSummarySheet = $spreadsheet->createSheet();
        $projectSummarySheet->setTitle('项目工时统计');
        $headerProjectSummary = ['项目名称', '项目人数', '项目总工时', '人均工时', '统计范围', '统计天数'];
        $projectSummarySheet->fromArray($headerProjectSummary, null, 'A1');
        $row = 2;
        $totalDaysInRange = count($allDates);
        foreach ($projectDetails as $pid => $details) {
            $projectSummarySheet->setCellValue('A'.$row, $details['name']);
            $activeUserCount = count($projectActiveUsers[$pid] ?? []);
            $projectSummarySheet->setCellValue('B'.$row, $activeUserCount);
            $totalHours = round($projectTotalHours[$pid] ?? 0.0, 2);
            $projectSummarySheet->setCellValue('C'.$row, $totalHours);
            $avgHoursPerUser = $activeUserCount > 0 ? round($totalHours / $activeUserCount, 2) : 0;
            $projectSummarySheet->setCellValue('D'.$row, $avgHoursPerUser);
            $projectSummarySheet->setCellValue('E'.$row, "{$startDate} ~ {$endDate}"); // 使用 ~ 分隔日期
            $projectSummarySheet->setCellValue('F'.$row, $totalDaysInRange);
            $row++;
        }

        // --- Sheet 3 onwards: 项目详情 Sheets ---
        foreach ($projectDetails as $pid => $details) {
            $projectSheet = $spreadsheet->createSheet();
            $safeSheetTitle = mb_substr(str_replace(['*', ':', '/', '\\', '?', '[', ']'], '_', $details['name']), 0, 30);
            if (empty($safeSheetTitle)) {
                $safeSheetTitle = "项目_{$pid}";
            }
            try {
                $projectSheet->setTitle($safeSheetTitle);
            } catch (\PhpOffice\PhpSpreadsheet\Exception $e) {
                $projectSheet->setTitle("项目_{$pid}_详情");
            }

            $headerProjectDetail = ['人员', '所属项目']; // 表头修改
            foreach ($allDates as $date) {
                $headerProjectDetail[] = $date;
            }
            $headerProjectDetail[] = '工时总计';
            $headerProjectDetail[] = '平均工时';
            $projectSheet->fromArray($headerProjectDetail, null, 'A1');

            $row = 2;
            $standardHoursForProject = $details['standard_hours'];
            $usersToDisplayInProjectSheet = $projectUserDataForSheets[$pid] ?? [];
            // 过滤掉在当前项目中没有工时的用户 (项目详情 Sheets)
            $usersToDisplayInProjectSheet = array_filter($usersToDisplayInProjectSheet, function ($userData) {
                return $userData['total_hours'] > 0.0;
            });
            // Sort users by ID for consistent order
            uksort($usersToDisplayInProjectSheet, fn($a, $b) => (int)$a <=> (int)$b);

            foreach ($usersToDisplayInProjectSheet as $userId => $data) {
                $col = 1;
                $projectSheet->setCellValue(Coordinate::stringFromColumnIndex($col++).$row, $data['name']);
                $projectSheet->setCellValue(Coordinate::stringFromColumnIndex($col++).$row, $data['project_name']); // 使用 project_name
                foreach ($allDates as $date) {
                    $hours = $data['daily_hours'][$date] ?? 0.0;
                    $cellCoordinate = Coordinate::stringFromColumnIndex($col).$row;
                    $projectSheet->setCellValue($cellCoordinate, $hours > 0 ? round($hours, 2) : 0);
                    if ($hours > 0) {
                        if ($hours >= $standardHoursForProject) {
                            $projectSheet->getStyle($cellCoordinate)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setARGB('FF99CC33'); // New Green
                        } else {
                            $projectSheet->getStyle($cellCoordinate)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setARGB('FFFF0000'); // New Red
                        }
                    }
                    $col++;
                }
                $projectSheet->setCellValue(Coordinate::stringFromColumnIndex($col++).$row, $data['total_hours'] > 0 ? round($data['total_hours'], 2) : 0);
                $avgHours = $totalDaysInRange > 0 ? round($data['total_hours'] / $totalDaysInRange, 2) : 0;
                $projectSheet->setCellValue(Coordinate::stringFromColumnIndex($col++).$row, $avgHours > 0 ? $avgHours : 0);
                $row++;
            }
        }

        if ($spreadsheet->getSheetCount() > 0) {
            $spreadsheet->setActiveSheetIndex(0);
        }

        $writer = new Xlsx($spreadsheet);
        $tempStream = fopen('php://temp', 'r+');
        $writer->save($tempStream);
        rewind($tempStream);
        $fileContent = stream_get_contents($tempStream);
        fclose($tempStream);

        return download($fileContent, rawurlencode("{$fileName}.xlsx"), true);
    }

}
