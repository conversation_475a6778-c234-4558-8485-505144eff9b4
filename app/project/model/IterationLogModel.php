<?php

/**
 * Desc: 类描述
 * @author: Plum
 * @date 2025/5/19
 */

namespace app\project\model;


use app\iterate\model\FlowStatusEnumModel;
use basic\BaseModel;
use traits\CreateAndUpdateModelTrait;

class IterationLogModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk   = 'log_id';
    protected $name = 'iteration_log';


    /**
     * 迭代周期变更日志
     * @param $iterationModel
     * @param $estimateStartTime
     * @param $estimateEndTime
     * @exception Exception
     * <AUTHOR>
     * @date 2025/5/19 11:52
     */
    public static function changeIterationCycleLog($iterationModel, $estimateStartTime, $estimateEndTime)
    {
        if ($iterationModel['estimate_start_time'] != $estimateStartTime || $iterationModel['estimate_end_time'] != $estimateEndTime){
            self::create([
                "iteration_id" => $iterationModel['iteration_id'],
                "remark"       => json_encode([
                    "key"   => "迭代预估周期  变更为",
                    "value" => $estimateStartTime . " ~ " . $estimateEndTime,
                ])
            ]);
        }
    }

    /**
     * 迭代状态变更日志
     * @param $iterationProcessNodeModel
     * @param $iterationModel
     * @param $statusEnumId
     * @exception Exception
     * <AUTHOR>
     * @date 2025/5/19 15:46
     */
    public static function changeIterationStatusLog($iterationProcessNodeModel, $iterationModel, $statusEnumId)
    {
        self::create([
            "iteration_id" => $iterationModel['iteration_id'],
            "remark"       => json_encode([
                "key"                       => "迭代状态  自动变更为",
                "iteration_process_node_id" => $iterationProcessNodeModel->iteration_process_node_id,
                "node_name"                 => $iterationProcessNodeModel->node_name,
                "value"                     => FlowStatusEnumModel::where('status_enum_id', $statusEnumId)->value('name'),
            ])
        ]);
    }


    /**
     * 获取迭代日志
     * @param $iterationId
     * @return IterationLogModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @exception Exception
     * <AUTHOR>
     * @date 2025/5/19 15:47
     */
    public static function getDataByIterationId($iterationId)
    {
        return self::where("iteration_id = {$iterationId} and is_delete = " . BaseModel::DELETE_NOT)
            ->order('create_at', 'desc')
            ->select();
    }
}
